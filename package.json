{"name": "tnkr-app-dashboard", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@vercel/sdk": "^1.6.0", "autoprefixer": "^10.4.21", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.485.0", "mongodb": "^6.16.0", "next": "15.2.4", "next-themes": "^0.4.6", "postcss": "^8.5.3", "raw-body": "^3.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "resend": "^4.1.2", "supabase": "^2.22.6", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "three": "^0.176.0", "tw-animate-css": "^1.2.5", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@eslint/js": "^9.25.1", "@iconify/react": "^6.0.0", "@next/eslint-plugin-next": "^15.3.1", "@octokit/rest": "^21.1.1", "@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/three": "^0.176.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.9.0", "dotenv": "^16.5.0", "eslint": "^8.47.0", "eslint-config-next": "^13.4.13", "eslint-config-prettier": "^8.10.0", "eslint-plugin-react": "^7.37.5", "file-loader": "^6.2.0", "globals": "^16.0.0", "pre-commit": "^1.2.2", "prettier": "^3.5.3", "tailwindcss": "^4.0.17", "typescript": "~5.5.0", "typescript-eslint": "^8.31.1"}, "pre-commit": ["lint", "type-check"]}