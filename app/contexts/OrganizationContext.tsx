'use client';

import React, { createContext, useContext } from 'react';
import type { PostgrestError } from '@supabase/supabase-js';
import { useOrganization } from '../utils/useOrganization'; // Adjust this path as needed
import type { Organization } from '../utils/useOrganization'; // Adjust this path as needed

// Define the shape of our context
interface OrganizationContextType {
  organization: Organization | null;
  loading: boolean;
  error: PostgrestError | null;
  refreshOrganization: () => Promise<void>;
}

// Create context with an undefined default so we can enforce provider usage
const OrganizationContext = createContext<OrganizationContextType | undefined>(
  undefined,
);

interface OrganizationProviderProps {
  children: React.ReactNode;
}

// OrganizationProvider wraps your app and provides organization data via the useOrganization hook
export const OrganizationProvider: React.FC<OrganizationProviderProps> = ({
  children,
}) => {
  const { organization, loading, error, refreshOrganization } =
    useOrganization();

  return (
    <OrganizationContext.Provider
      value={{ organization, loading, error, refreshOrganization }}
    >
      {children}
    </OrganizationContext.Provider>
  );
};

// Custom hook for consuming the context
export const useTnkrOrganization = (): OrganizationContextType => {
  const context = useContext(OrganizationContext);
  if (!context) {
    throw new Error(
      'useTnkrOrganization must be used within an OrganizationProvider',
    );
  }
  return context;
};
