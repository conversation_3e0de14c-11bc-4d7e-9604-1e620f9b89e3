'use client';

import React from 'react';
import { Button } from '@/app/components/ui/Button';
import { Check } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Offering {
  text: string;
  icon?: React.ReactNode;
}

interface PricingCardProps {
  name: string;
  price: string | number;
  description: string;
  offerings: Offering[];
  isCurrentPlan?: boolean;
  discount?: string;
  showDiscount?: boolean;
  variant?: 'current' | 'upgrade' | 'contact';
  onAction?: () => void;
  className?: string;
}

export function PricingCard({
  name,
  price,
  description,
  offerings,
  isCurrentPlan,
  discount,
  showDiscount,
  variant = 'upgrade',
  onAction,
  className,
}: PricingCardProps) {
  const calculateDiscountedPrice = (
    originalPrice: number,
    discountString: string,
  ) => {
    const discountPercentage = parseInt(discountString);
    if (!isNaN(discountPercentage)) {
      return originalPrice * (1 - discountPercentage / 100);
    }
    return originalPrice;
  };

  const displayPrice =
    typeof price === 'number' && discount && showDiscount
      ? calculateDiscountedPrice(price, discount)
      : price;

  const renderActionButton = () => {
    switch (variant) {
      case 'current':
        return (
          <div className='bg-green-500/10 rounded-lg py-1 px-3 mb-6 w-fit'>
            <span className='text-green-500 text-sm font-medium'>
              Current Plan
            </span>
          </div>
        );
      case 'contact':
        return (
          <Button
            variant='outline'
            size='default'
            className='mb-6 max-w-[6rem]'
            onClick={onAction}
          >
            Contact us
          </Button>
        );
      default:
        return (
          <Button
            variant='default'
            size='default'
            className='mb-6 max-w-[6rem] bg-[#AA423A]'
            onClick={onAction}
          >
            Upgrade
          </Button>
        );
    }
  };

  return (
    <div
      className={cn(
        'p-6 rounded-3xl border border-border flex flex-col relative overflow-hidden',
        isCurrentPlan && 'bg-[#161617]',
        className,
      )}
    >
      {discount && showDiscount && (
        <div className='absolute top-3 right-3 bg-[#DB783E]/20 text-[#DB783E] text-xs px-2 py-0.5 rounded'>
          {discount}
        </div>
      )}

      <h3 className='text-xl font-bold mb-3'>{name}</h3>
      <div className='mb-4'>
        {typeof displayPrice === 'number' ? (
          <div className='flex items-baseline gap-2'>
            {showDiscount && typeof price === 'number' && (
              <span className='text-lg line-through text-white/50'>
                ${price}
              </span>
            )}
            <span className='text-3xl font-bold'>${displayPrice}</span>
            <span className='text-muted-foreground text-sm text-white/50'>
              /month
            </span>
          </div>
        ) : (
          <span className='text-2xl font-bold'>{displayPrice}</span>
        )}
      </div>
      <p className='text-muted-foreground text-base mb-6 text-white/50 max-w-[12rem]'>
        {description}
      </p>

      {renderActionButton()}

      <div className='space-y-5 mb-6 flex-grow'>
        {offerings.map((offering, index) => (
          <div key={index} className='flex items-start gap-2'>
            {offering.icon || (
              <Check className='text-green-500 size-4 mt-0.5 shrink-0' />
            )}
            <span className='text-sm'>{offering.text}</span>
          </div>
        ))}
      </div>
    </div>
  );
}
