// ActivityHistory.tsx
'use client';

import React, { useState } from 'react';
import { Badge } from '@/app/components/ui/badge';
import { Card, CardContent } from '@/app/components/ui/card';
import {
  CheckCircle,
  Eye,
  History,
  Lightbulb,
  RefreshCcw,
  Rocket,
} from 'lucide-react';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/app/components/ui/tabs';
import { cn } from '@/lib/utils';
import TnkrTable from '@/app/components/ui/TnkrTable';

export interface ActivityItem {
  id: string;
  type: string;
  title: string;
  date: string;
  status: string;
  changes?: string;
  icon?: string;
}

interface ActivityHistoryProps {
  data: ActivityItem[];
  title?: string;
  description?: string;
  onTabChange?: (tab: string) => void;
}

const ActivityHistory: React.FC<ActivityHistoryProps> = ({
  data,
  title = 'Activity History',
  description = 'Keep an eye on changes and updates made to your docs',
  onTabChange,
}) => {
  const [activeTab, setActiveTab] = useState<string>('live');

  // Handler for tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    // Call the onTabChange prop if provided
    if (onTabChange) {
      onTabChange(value);
    }
  };

  const getIcon = (type: string | undefined) => {
    switch (type) {
      case 'rocket':
        return (
          <div className='bg-[#024A43]/20 w-8 h-8 flex items-center justify-center rounded-full'>
            <Lightbulb className='text-[#00B453] w-5 h-5 ' />
          </div>
        );
      case 'history':
        return (
          <div className='bg-[#AA423A]/20 p-2 rounded-full text-center'>
            <RefreshCcw className='h-6 w-6 text-[#AA423A]' />
          </div>
        );
      default:
        return (
          <div className='bg-gray-900 p-2 rounded-full'>
            <History className='h-6 w-6 text-gray-500' />
          </div>
        );
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'successful':
        return (
          <Badge
            variant='outline'
            className='bg-[#101B14] text-[#00B453] border-0 text-sm px-4'
          >
            <CheckCircle className='h-4 w-4 mr-1' />
            Successful
          </Badge>
        );
      case 'pending':
        return (
          <Badge
            variant='outline'
            className='bg-yellow-100 text-yellow-800 border-yellow-200'
          >
            Pending
          </Badge>
        );
      case 'failed':
        return (
          <Badge
            variant='outline'
            className='bg-red-100 text-red-800 border-red-200'
          >
            Failed
          </Badge>
        );
      default:
        return (
          <Badge variant='outline' className='bg-gray-100 text-gray-800'>
            {status}
          </Badge>
        );
    }
  };

  return (
    <Tabs
      defaultValue='live'
      className='w-full'
      onValueChange={handleTabChange}
    >
      <div className='flex flex-row w-full justify-between mb-4'>
        <div>
          <div className='text-xl font-normal'>{title}</div>
          <p className='text-white/70 text-md mt-1'>{description}</p>
        </div>
        <div>
          <TabsList className='py-4 px-3 bg-[#161617]'>
            <TabsTrigger
              value='live'
              className={cn(
                'py-3 text-sm font-light border-0 hover:bg-[#232326] cursor-pointer',
                activeTab === 'live'
                  ? 'bg-[#232326] text-white'
                  : 'bg-[#161617]',
              )}
            >
              <Rocket className='h-3 w-3' />
              Live
            </TabsTrigger>
            <TabsTrigger
              value='previews'
              className={cn(
                'py-3 text-sm font-light border-0 hover:bg-[#232326] cursor-pointer',
                activeTab === 'previews'
                  ? 'bg-[#232326] text-white'
                  : 'bg-[#161617]',
              )}
            >
              <Eye className='h-3 w-3' />
              Previews
            </TabsTrigger>
          </TabsList>
        </div>
      </div>

      {/* Tab content is placed here, below the header section */}
      <TabsContent value='live'>
        <Card className='w-full text-white border-none p-0'>
          <CardContent className='p-0'>
            <TnkrTable
              noBorder={true}
              columns={[
                {
                  header: 'Activity',
                  render: (item: ActivityItem) => (
                    <div className='flex items-center gap-3'>
                      {getIcon(item.icon)}
                      <div>
                        <div className='font-medium'>{item.title}</div>
                        <div className='text-sm text-gray-400'>{item.date}</div>
                      </div>
                    </div>
                  ),
                },
                {
                  header: 'Status',
                  render: (item: ActivityItem) => getStatusBadge(item.status),
                },
                {
                  header: 'Changes',
                  render: (item: ActivityItem) => item.changes || '—',
                },
              ]}
              data={data}
            />
          </CardContent>
        </Card>
      </TabsContent>
      <TabsContent value='previews'>
        <Card className='w-full text-white border-none p-0'>
          <CardContent className='py-6'>
            <div className='text-center text-gray-400'>Coming soon</div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  );
};

export default ActivityHistory;
