interface IntroHeaderProps {
  title: string;
  description: string;
  hasDivider?: boolean;
}

export function IntroHeader({
  title,
  description,
  hasDivider = true,
}: IntroHeaderProps) {
  return (
    <div className='space-y-2 mb-8'>
      <h1 className='text-xl font-medium  text-white'>{title}</h1>
      <p className='text-white/70 mt-5'>{description}</p>
      {hasDivider && <div className='border-t border-white/10 mt-5'></div>}
    </div>
  );
}
