'use client';

import type React from 'react';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState } from 'react';
import { cn } from '@/lib/utils';
import {
  Home,
  BarChart3,
  Settings,
  Users,
  MessageSquareMore,
  PanelLeftClose,
  PanelRightClose,
  ChevronLeft,
  Globe,
  GitBranch,
  CreditCard,
  Shield,
} from 'lucide-react';
import { CoralCircleicon } from './CoralCirclIcon';
import { signOutAction } from '@/app/actions';
import { ComingSoonWrapper } from '@/app/components/ui/ComingSoonWrapper';
import { Icon as CustomIcon } from './CustomIcon';
import EditorSvg from '@/assets/icons/editor.svg';
import CubeSvg from '@/assets/icons/cube.svg';
import LearnSvg from '@/assets/icons/learn.svg';
import LogoutSvg from '@/assets/icons/logout.svg';
interface NavItemProps {
  href?: string;
  icon: React.ComponentType<{ className?: string }>;
  children: React.ReactNode;
  isCollapsed?: boolean;
  isActive?: boolean;
  onClick?: () => void;
  onShowSecondaryNav?: () => void;
}

function NavItem({
  href,
  icon: Icon,
  children,
  isActive,
  isCollapsed,
  onClick,
  onShowSecondaryNav,
}: NavItemProps) {
  const [signOutLoading, setSignOutLoading] = useState(false);

  const handleClick = async () => {
    if (onShowSecondaryNav) {
      onShowSecondaryNav();
      return;
    }

    if (onClick) {
      try {
        setSignOutLoading(true);
        onClick();
      } catch (err) {
        console.error('NavItem error:', err);
      } finally {
        setSignOutLoading(false);
      }
    }
  };

  const className = cn(
    'flex items-center gap-3 rounded-md cursor-pointer px-3 py-2 text-sm transition-colors text-white/70',
    isActive ? 'text-[#AA423A]' : 'hover:text-white',
    isCollapsed && 'justify-center px-2',
  );

  if (onClick || onShowSecondaryNav) {
    return (
      <button
        type='button'
        onClick={handleClick}
        className={className}
        title={isCollapsed ? String(children) : undefined}
      >
        <Icon
          className={cn(
            'h-4 w-4',
            isActive ? 'text-[#AA423A]' : 'text-muted-foreground',
          )}
        />
        {!isCollapsed && (
          <span>{signOutLoading ? 'Signing out...' : children}</span>
        )}
      </button>
    );
  }

  return (
    <Link
      href={href || '#'}
      className={className}
      title={isCollapsed ? String(children) : undefined}
    >
      <Icon
        className={cn(
          'h-4 w-4',
          isActive ? 'text-[#AA423A]' : 'text-muted-foreground',
        )}
      />
      {!isCollapsed && <span>{children}</span>}
    </Link>
  );
}

NavItem.displayName = 'NavItem';

// Add this reusable component for consistent navigation styling
function NavSection({
  children,
  isCollapsed,
}: {
  children: React.ReactNode;
  isCollapsed: boolean;
}) {
  return (
    <nav
      className={cn(
        'flex flex-col space-y-1',
        isCollapsed ? 'items-center px-1' : 'px-4',
      )}
    >
      {children}
    </nav>
  );
}

interface SecondaryNavProps {
  title: string;
  children: React.ReactNode;
  onBack: () => void;
  isCollapsed: boolean;
  orgName: string;
  toggleSidebar: () => void;
}

function SecondaryNav({
  children,
  onBack,
  isCollapsed,
  orgName,
  toggleSidebar,
}: SecondaryNavProps) {
  return (
    <div className='flex flex-col h-full'>
      {/* Secondary Nav Header */}
      <div className='flex items-center justify-between h-14 px-4 mt-2'>
        {isCollapsed ? (
          <button
            onClick={(e) => {
              e.stopPropagation();
              toggleSidebar();
            }}
            className='w-full flex items-center justify-center h-8 text-gray-500 hover:text-gray-300 transition-colors cursor-pointer'
            aria-label='Expand sidebar'
          >
            <PanelRightClose className='h-5 w-5' />
          </button>
        ) : (
          <>
            <div className='flex items-center gap-2'>
              <div className='w-8 h-8 rounded-full bg-gradient-to-b from-[#AA423A] to-[#441A17] flex items-center justify-center'>
                <div className='w-7 h-7 rounded-full bg-black flex items-center justify-center relative'>
                  <CoralCircleicon className='w-6 h-6' />
                </div>
              </div>
              <span className='text-lg font-medium text-white'>{orgName}</span>
            </div>
            <button
              onClick={(e) => {
                e.stopPropagation();
                toggleSidebar();
              }}
              className='h-5 w-5 text-gray-500 hover:text-gray-300 transition-colors cursor-pointer ml-2'
              aria-label='Collapse sidebar'
            >
              <PanelLeftClose className='h-5 w-5' />
            </button>
          </>
        )}
      </div>

      {/* Secondary Nav Content */}
      <div className={cn('flex-1 py-6')}>
        <div className='flex flex-col space-y-4 mt-10'>
          {isCollapsed ? (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onBack();
              }}
              className='flex items-center mx-auto text-white/70 hover:text-white transition-colors'
              aria-label='Back to main navigation'
            >
              <ChevronLeft className='h-5 w-5' />
            </button>
          ) : (
            <button
              onClick={onBack}
              className='flex items-center gap-3 ml-5 cursor-pointer text-white/70 mb-10 hover:text-white transition-colors'
              aria-label='Back to main navigation'
            >
              <ChevronLeft className='h-5 w-5' />
              <span>Main Menu</span>
            </button>
          )}
          <NavSection isCollapsed={isCollapsed}>{children}</NavSection>
        </div>
      </div>
    </div>
  );
}

interface SidebarNavigationProps {
  orgName: string;
  isCollapsed: boolean;
  onToggleAction: () => void;
}

export function SidebarNavigation({
  orgName,
  isCollapsed,
  onToggleAction,
}: SidebarNavigationProps) {
  const pathname = usePathname();
  const [, setSignOutLoading] = useState(false);
  const [showSecondaryNav, setShowSecondaryNav] = useState(false);
  const [activeSecondaryNav, setActiveSecondaryNav] = useState<
    'settings' | 'editor' | null
  >(null);

  const toggleSidebar = () => {
    onToggleAction();
    // Force regeneration of secondary nav content when collapse state changes
    if (showSecondaryNav) {
      // This will make the component re-render with the updated isCollapsed state
      setShowSecondaryNav(false);
      setTimeout(() => {
        setShowSecondaryNav(true);
      }, 10);
    }
  };

  const handleSignOut = async () => {
    try {
      setSignOutLoading(true);
      await signOutAction();
    } catch (err) {
      console.error('Error during sign out:', err);
    } finally {
      setSignOutLoading(false);
    }
  };

  const showSettingsNav = () => {
    setActiveSecondaryNav('settings');
    setShowSecondaryNav(true);
  };

  const showEditorNav = () => {
    setActiveSecondaryNav('editor');
    setShowSecondaryNav(true);
  };

  const hideSecondaryNav = () => {
    setShowSecondaryNav(false);
    setActiveSecondaryNav(null);
  };

  // Generate the secondary nav content for Settings
  const generateSettingsNavContent = () => (
    <>
      <NavItem
        href='/overview/settings/domain'
        icon={Globe}
        isCollapsed={isCollapsed}
        isActive={pathname === '/overview/settings/domain'}
      >
        Custom Domain
      </NavItem>

      <NavItem
        href='/overview/settings/git'
        icon={GitBranch}
        isCollapsed={isCollapsed}
        isActive={pathname === '/overview/settings/git'}
      >
        Git Settings
      </NavItem>
      <NavItem
        href='/overview/settings/members'
        icon={Users}
        isCollapsed={isCollapsed}
        isActive={pathname === '/overview/settings/members'}
      >
        Members
      </NavItem>
      <NavItem
        href='/overview/settings/billing'
        icon={CreditCard}
        isCollapsed={isCollapsed}
        isActive={pathname === '/overview/settings/billing'}
      >
        Billing
      </NavItem>
      <NavItem
        href='/overview/settings/security'
        icon={Shield}
        isCollapsed={isCollapsed}
        isActive={pathname === '/overview/settings/security'}
      >
        Security
      </NavItem>
      <NavItem
        href='/overview/settings/general'
        icon={Settings}
        isCollapsed={isCollapsed}
        isActive={pathname === '/overview/settings/general'}
      >
        General
      </NavItem>
    </>
  );

  // Generate the secondary nav content for Editor
  const generateEditorNavContent = () => (
    <>
      <NavItem
        href='/overview/editor/hardware-docs'
        icon={() => (
          <CustomIcon
            component={CubeSvg}
            color={
              pathname === '/overview/editor/hardware-docs'
                ? '#AA423A'
                : 'current-color'
            }
            size={18}
          />
        )}
        isCollapsed={isCollapsed}
        isActive={pathname?.includes('/overview/editor/hardware-docs')}
      >
        Hardware Docs
      </NavItem>
      <NavItem
        href='/overview/editor/central-docs'
        icon={BarChart3}
        isCollapsed={isCollapsed}
        isActive={pathname === '/overview/editor/central-docs'}
      >
        Central Docs
      </NavItem>
    </>
  );

  // Get the appropriate content based on active secondary nav
  const getSecondaryNavContent = () => {
    switch (activeSecondaryNav) {
      case 'settings':
        return generateSettingsNavContent();
      case 'editor':
        return generateEditorNavContent();
      default:
        return null;
    }
  };

  // Get the title for the secondary nav
  const getSecondaryNavTitle = () => {
    switch (activeSecondaryNav) {
      case 'settings':
        return 'Settings';
      case 'editor':
        return 'Editor';
      default:
        return '';
    }
  };

  return (
    <div
      className={cn(
        'fixed top-0 flex flex-col h-screen transition-all duration-300 overflow-y-auto',
        isCollapsed ? 'min-w-[60px]' : 'w-[240px]',
      )}
      onClick={isCollapsed ? toggleSidebar : undefined}
    >
      <div className='relative w-full h-full overflow-hidden'>
        {/* Main Navigation Panel */}
        <div
          className={cn(
            'absolute top-0 left-0 w-full h-full flex flex-col transition-transform duration-300 ease-in-out',
            showSecondaryNav ? '-translate-x-full' : 'translate-x-0',
          )}
        >
          {/* Header */}
          <div className='flex items-center justify-between h-14 px-4 mt-2'>
            {isCollapsed ? (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  toggleSidebar();
                }}
                className='w-full flex items-center justify-center h-8 text-gray-500 hover:text-gray-300 transition-colors cursor-pointer'
                aria-label='Expand sidebar'
              >
                <PanelRightClose className='h-5 w-5' />
              </button>
            ) : (
              <>
                <div className='flex items-center gap-2'>
                  <div className='w-8 h-8 rounded-full bg-gradient-to-b from-[#AA423A] to-[#441A17] flex items-center justify-center'>
                    <div className='w-7 h-7 rounded-full bg-black flex items-center justify-center relative'>
                      <CoralCircleicon className='w-6 h-6' />
                    </div>
                  </div>
                  <span className='text-lg font-medium text-white'>
                    {orgName}
                  </span>
                </div>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleSidebar();
                  }}
                  className='h-5 w-5 text-gray-500 hover:text-gray-300 transition-colors cursor-pointer'
                  aria-label='Collapse sidebar'
                >
                  <PanelLeftClose className='h-5 w-5' />
                </button>
              </>
            )}
          </div>

          {/* Main Navigation */}
          <div className='flex-1 py-6' onClick={(e) => e.stopPropagation()}>
            <NavSection isCollapsed={isCollapsed}>
              <NavItem
                href='/overview'
                icon={Home}
                isActive={pathname === '/overview'}
                isCollapsed={isCollapsed}
              >
                Overview
              </NavItem>
              <NavItem
                icon={() => (
                  <CustomIcon
                    component={EditorSvg}
                    color={
                      pathname?.includes('/overview/editor')
                        ? '#AA423A'
                        : 'current-color'
                    }
                    size={18}
                  />
                )}
                isActive={pathname?.includes('/overview/editor')}
                isCollapsed={isCollapsed}
                onShowSecondaryNav={showEditorNav}
              >
                Editor
              </NavItem>
              <ComingSoonWrapper>
                <NavItem
                  href='#'
                  icon={BarChart3}
                  isActive={pathname === '/analytics'}
                  isCollapsed={isCollapsed}
                >
                  Analytics
                </NavItem>
              </ComingSoonWrapper>
              <ComingSoonWrapper>
                <NavItem
                  href='#'
                  icon={() => <CustomIcon component={LearnSvg} size={18} />}
                  isActive={pathname === '/learn'}
                  isCollapsed={isCollapsed}
                >
                  Learn
                </NavItem>
              </ComingSoonWrapper>
              <NavItem
                icon={Settings}
                isActive={pathname?.includes('/overview/settings')}
                isCollapsed={isCollapsed}
                onShowSecondaryNav={showSettingsNav}
              >
                Settings
              </NavItem>
            </NavSection>
          </div>

          {/* Footer Navigation */}
          <div className='py-4' onClick={(e) => e.stopPropagation()}>
            <NavSection isCollapsed={isCollapsed}>
              <ComingSoonWrapper>
                <NavItem href='#' icon={Users} isCollapsed={isCollapsed}>
                  Invite Members
                </NavItem>
              </ComingSoonWrapper>
              <ComingSoonWrapper>
                <NavItem
                  href='#'
                  icon={MessageSquareMore}
                  isCollapsed={isCollapsed}
                >
                  Chat with Founders
                </NavItem>
              </ComingSoonWrapper>
              <NavItem
                onClick={handleSignOut}
                href='#'
                icon={() => <CustomIcon component={LogoutSvg} size={18} />}
                isCollapsed={isCollapsed}
              >
                Logout
              </NavItem>
            </NavSection>
          </div>
        </div>

        {/* Secondary Navigation Panel */}
        <div
          className={cn(
            'absolute top-0 left-0 w-full h-full flex flex-col transition-transform duration-300 ease-in-out',
            showSecondaryNav ? 'translate-x-0' : 'translate-x-full',
          )}
        >
          <SecondaryNav
            title={getSecondaryNavTitle()}
            onBack={hideSecondaryNav}
            isCollapsed={isCollapsed}
            orgName={orgName}
            toggleSidebar={toggleSidebar}
          >
            {getSecondaryNavContent()}
          </SecondaryNav>
        </div>
      </div>
    </div>
  );
}
