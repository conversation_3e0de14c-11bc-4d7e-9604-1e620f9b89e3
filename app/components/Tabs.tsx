import React, { useState } from 'react';

export interface TabItem {
  /** Label shown in the tab header */
  label: string;
  /** Content rendered when this tab is active */
  content: React.ReactNode;
}

export interface TabsProps {
  /** Array of tabs to render */
  tabs: TabItem[];
  /** Zero-based index of the initially active tab (default: 0) */
  initialActiveIndex?: number;
  /** Optional callback when active tab changes */
  onTabChange?: (newIndex: number) => void;
}

const Tabs: React.FC<TabsProps> = ({
  tabs,
  initialActiveIndex = 0,
  onTabChange,
}) => {
  const [activeIndex, setActiveIndex] = useState(initialActiveIndex);

  const handleClick = (index: number) => {
    setActiveIndex(index);
    onTabChange?.(index);
  };

  return (
    <div>
      {/* Tab headers */}
      <div className='flex px-5'>
        {tabs.map((tab, idx) => (
          <button
            key={idx}
            onClick={() => handleClick(idx)}
            className={`px-4 py-2 text-sm font-medium cursor-pointer transition-colors duration-150 ${
              activeIndex === idx
                ? 'text-[#DB783E] bg-[#DB783E33] rounded-tl-lg rounded-tr-lg'
                : 'text-white/70 hover:text-gray-200'
            }`}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Active tab content */}
      <div>{tabs[activeIndex]?.content}</div>
    </div>
  );
};

export { Tabs };
