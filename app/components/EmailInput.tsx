'use client';
import React, { useState } from 'react';
import { cn } from '@/app/lib/utils';
import { TagInput } from './ui/TagInput';
import { Mail } from 'lucide-react';

interface EmailInputProps {
  value: string[];
  onChange: (emails: string[]) => void;
  placeholder?: string;
  error?: string;
  disabled?: boolean;
  className?: string;
  max?: number;
  id?: string;
}

const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

const EmailInput: React.FC<EmailInputProps> = ({
  value,
  onChange,
  placeholder = '<EMAIL>, <EMAIL>',
  error,
  disabled,
  className,
  max,
  id,
}) => {
  const [, setInputValue] = useState('');
  const [localError, setLocalError] = useState<string | null>(null);

  const handleChange = (newEmails: string[]) => {
    onChange(newEmails);
    setLocalError(null);
  };

  const handleInputChange = (value: string) => {
    setInputValue(value);

    // Clear error when user starts typing
    if (localError) {
      setLocalError(null);
    }
  };

  const handleValidation = (email: string): boolean => {
    if (!validateEmail(email)) {
      setLocalError(`Invalid email format: ${email}`);
      return false;
    }
    return true;
  };

  return (
    <div className={className}>
      <TagInput
        id={id}
        value={value}
        onChange={handleChange}
        onInputChange={handleInputChange}
        validator={handleValidation}
        placeholder={placeholder}
        delimiter={/[,\s]/}
        allowPaste={true}
        max={max}
        disabled={disabled}
        startAdornment={<Mail className='h-4 w-4 text-[#8E9196]' />}
        tagClassName='bg-[#313133] text-white hover:bg-[#3a3a3c]'
        className={cn(
          'border border-[#313133]',
          (error || localError) && 'border-red-500 focus-within:ring-red-500',
        )}
      />
      {(error || localError) && (
        <p className='text-red-500 text-xs mt-1'>{error || localError}</p>
      )}
    </div>
  );
};

export { EmailInput, validateEmail };
