import React from 'react';
import { Button } from '@/app/components/ui/Button';

interface EmptyStateProps {
  message: string;
  title: string;
  buttonText?: string;
  buttonIcon?: React.ReactNode;
  onButtonClick?: () => void;
}
export const EmptyState = ({
  title,
  message,
  buttonIcon,
  buttonText,
  onButtonClick,
}: EmptyStateProps) => {
  return (
    <div className='flex  w-full flex-col min-h-[80vh] items-center justify-center  text-white/70'>
      <h3 className='font-medium text-xl mb-4'>{title}</h3>
      <p className='mb-4'>{message}</p>
      <Button
        startIcon={buttonIcon}
        variant='outline'
        size='lg'
        style={{
          borderColor: '#AA423A',
        }}
        className='mt-6 border  text-white/70 px-4 py-2 rounded-lg hover:bg-[#AA423A] hover:text-white transition'
        onClick={onButtonClick}
      >
        {buttonText}
      </Button>
    </div>
  );
};
