import React, { useState, useRef, useEffect } from 'react';
import { Chevron } from '@/app/components/Chevron';

interface Option {
  value: string;
  label: string;
  logo?: React.ReactNode;
}

interface SelectDropdownProps {
  options: Option[];
  value?: Option;
  onChange: (option: Option) => void;
  placeholder?: string;
  label?: string;
}

const TnkrSelect: React.FC<SelectDropdownProps> = ({
  options,
  value,
  onChange,
  placeholder = 'Select...',
  label,
}: SelectDropdownProps) => {
  const [open, setOpen] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (ref.current && !ref.current.contains(e.target as Node))
        setOpen(false);
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className='relative w-full' ref={ref}>
      {label && (
        <label className='text-sm font-medium text-white'>{label}</label>
      )}
      <button
        type='button'
        onClick={() => setOpen((o) => !o)}
        className='w-full cursor-pointer mt-4 flex items-center justify-between px-4 py-2 focus:ring-2 focus:ring-white rounded-lg border-[#313133] border focus:outline-none'
      >
        <div className='flex items-center space-x-2'>
          {value?.logo && value.logo}
          <span className='text-white'>
            {value ? value.label : placeholder}
          </span>
        </div>
        <Chevron isOpen={open} />
      </button>
      {open && (
        <div
          className='absolute z-50 mt-1 w-full bg-black border-[#313133] rounded shadow-lg max-h-60 overflow-y-auto'
          style={{ top: '100%' }} // Ensure dropdown is positioned below the button
        >
          {options.map((opt) => (
            <button
              key={opt.value}
              type='button'
              onClick={() => {
                onChange(opt);
                setOpen(false);
              }}
              className='w-full flex items-center px-4 py-2 cursor-pointer hover:bg-[#313133] focus:bg-[#313133] border-b border-[#313133] last:border-b-0 text-left'
            >
              {opt.logo && <span className='w-5 h-5 mr-2'>{opt.logo}</span>}
              <span className='text-white'>{opt.label}</span>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export { TnkrSelect };
