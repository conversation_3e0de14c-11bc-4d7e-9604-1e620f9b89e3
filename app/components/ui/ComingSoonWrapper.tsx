import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  Toolt<PERSON><PERSON>rovider,
  TooltipTrigger,
} from '@/app/components/ui/tooltip';
import { TooltipArrow } from '@radix-ui/react-tooltip';
interface ComingSoonWrapperProps {
  children: React.ReactNode;
  message?: string;
}

// A wrapper that intercepts click events and shows a tooltip saying "Coming Soon"
export function ComingSoonWrapper({
  children,
  message = 'Coming Soon',
}: ComingSoonWrapperProps) {
  // This prevents any click event from triggering the underlying component's action
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    return false;
  };

  // For NavItem components, we need to ensure the href is disabled
  const childrenWithDisabledLinks = React.Children.map(children, (child) => {
    if (React.isValidElement(child)) {
      // If it's a NavItem component, clone it with href="#" to prevent navigation
      if (
        child.type &&
        typeof child.type !== 'string' &&
        'displayName' in child.type &&
        child.type.displayName === 'NavItem'
      ) {
        return React.cloneElement(
          child as React.ReactElement<{
            href?: string;
            onClick?: (e: React.MouseEvent) => void;
          }>,
          {
            href: '#',
            onClick: (e: React.MouseEvent) => {
              e.preventDefault();
              e.stopPropagation();
            },
          },
        );
      }

      // For other components, just pass them through
      return child;
    }
    return child;
  });

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          {/* Wrap in a div that intercepts clicks */}
          <div
            onClick={handleClick}
            style={{ display: 'inline-block' }}
            className='cursor-not-allowed'
          >
            {childrenWithDisabledLinks}
          </div>
        </TooltipTrigger>
        <TooltipContent
          side='right'
          className='border-0 bg-white text-black text-base'
        >
          {message}
          <TooltipArrow className='fill-white' />
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
