'use client';

import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Check } from 'lucide-react';
import { Button } from '@/app/components/ui/Button';

interface SelectOption {
  value: string;
  label: string;
  description?: string;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
}

interface CtaButtonProps {
  label: string;
  icon?: React.ReactNode;
  onClick: (value: string) => void;
  variant?: 'default' | 'destructive';
  textColor?: string;
  hoverBgColor?: string;
  customClass?: string;
}

interface SelectAccordionProps {
  options: Array<SelectOption>;
  defaultValue?: string;
  placeholder?: string;
  variant?: 'outlined' | 'bare';
  onChange?: (value: string) => void;
  className?: string;
  disabled?: boolean;
  ctaButton?: CtaButtonProps | React.ReactNode;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
}

const SelectAccordion: React.FC<SelectAccordionProps> = ({
  options,
  defaultValue = '',
  placeholder = 'Select an option',
  variant = 'outlined',
  onChange,
  className,
  disabled = false,
  ctaButton,
  startIcon,
  endIcon,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedValue, setSelectedValue] = useState(defaultValue);
  const wrapperRef = useRef<HTMLDivElement>(null);

  const selectedOption = options.find(
    (option) => option.value === selectedValue,
  );
  const selectedLabel = selectedOption?.label || placeholder;

  // Extract font size from className if provided
  const hasFontSizeClass = className?.includes('text-');
  const fontSizeClass = hasFontSizeClass ? '' : 'text-base';

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        wrapperRef.current &&
        !wrapperRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSelect = (value: string) => {
    setSelectedValue(value);
    if (onChange) {
      onChange(value);
    }
    setIsOpen(false);
  };

  // Render CTA button based on type
  const renderCtaButton = () => {
    if (!ctaButton) return null;

    // If ctaButton is a ReactNode, render it directly
    if (React.isValidElement(ctaButton)) {
      return <div className='mt-2'>{ctaButton}</div>;
    }

    // Otherwise, treat it as CtaButtonProps
    const ctaProps = ctaButton as CtaButtonProps;
    return (
      <div className='mt-4'>
        <Button
          className={cn(
            'w-full flex items-center gap-2 cursor-pointer px-2 py-2 rounded-xl text-left',
            fontSizeClass,
            ctaProps.customClass,
          )}
          variant={ctaProps.variant || 'default'}
          onClick={() => {
            ctaProps.onClick(selectedValue);
            setIsOpen(false);
          }}
        >
          {ctaProps.icon}
          {ctaProps.label}
        </Button>
      </div>
    );
  };

  return (
    <div
      ref={wrapperRef}
      className={cn(
        'relative w-full',
        disabled && 'opacity-50 cursor-not-allowed',
        className,
      )}
    >
      <div
        className={cn(
          'flex items-center cursor-pointer p-3',
          variant === 'outlined' &&
            'border border-[#313133] rounded-xl focus:ring-2 focus:ring-white',
          variant === 'bare' && 'border-none',
          disabled && 'cursor-not-allowed',
        )}
        onClick={() => !disabled && setIsOpen(!isOpen)}
      >
        <div
          className={cn(
            'flex items-center w-full',
            variant === 'bare' ? 'gap-4' : 'justify-between',
          )}
        >
          <div className='flex items-center gap-3'>
            {startIcon && <span className='flex-shrink-0'>{startIcon}</span>}
            {selectedOption?.startIcon && (
              <span className='flex-shrink-0'>{selectedOption.startIcon}</span>
            )}
            <span
              className={cn(
                fontSizeClass,
                'font-medium',
                selectedValue ? 'text-white' : 'text-white/50',
              )}
            >
              {selectedLabel}
            </span>
          </div>

          <div className='flex items-center gap-2'>
            {selectedOption?.endIcon && (
              <span className='flex-shrink-0'>{selectedOption.endIcon}</span>
            )}
            {endIcon && <span className='flex-shrink-0'>{endIcon}</span>}
            <svg
              className={`w-5 h-5 transition-transform ${isOpen ? 'rotate-180' : ''}`}
              fill='none'
              stroke='currentColor'
              viewBox='0 0 24 24'
              xmlns='http://www.w3.org/2000/svg'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M19 9l-7 7-7-7'
              />
            </svg>
          </div>
        </div>
      </div>

      {isOpen && !disabled && (
        <div className='absolute z-[200] w-full  mt-1 bg-[#161617] border border-[#313133] rounded-2xl px-3 py-3 shadow-lg overflow-hidden max-h-96 overflow-y-auto'>
          <div className='space-y-2'>
            {options.map((option) => (
              <div
                key={option.value}
                className={cn(
                  'relative px-4 py-3 cursor-pointer hover:bg-[#222224] rounded-xl transition-colors',
                  selectedValue === option.value && 'bg-[#AA423A]/20',
                )}
                onClick={() => handleSelect(option.value)}
              >
                <div className='flex justify-between items-start'>
                  <div className='flex items-center gap-2'>
                    {option.startIcon && (
                      <span className='flex-shrink-0'>{option.startIcon}</span>
                    )}
                    <div>
                      <h3
                        className={cn(fontSizeClass, 'font-normal text-white')}
                      >
                        {option.label}
                      </h3>
                      {option.description && (
                        <p
                          className={cn(
                            hasFontSizeClass ? 'text-sm' : fontSizeClass,
                            'text-white/70 mt-1',
                          )}
                        >
                          {option.description}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className='flex items-center gap-2'>
                    {option.endIcon && (
                      <span className='flex-shrink-0'>{option.endIcon}</span>
                    )}
                    {selectedValue === option.value && (
                      <Check className='h-5 w-5 text-white' />
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {renderCtaButton()}
        </div>
      )}
    </div>
  );
};

export default SelectAccordion;
