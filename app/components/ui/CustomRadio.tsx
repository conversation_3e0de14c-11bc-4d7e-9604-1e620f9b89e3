'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';

interface CustomRadioProps {
  checked?: boolean;
  onChange?: (checked: boolean) => void;
  trackClassName?: string;
  knobClassName?: string;
  className?: string;
  disabled?: boolean;
  name?: string;
  value?: string;
  id?: string;
}

export const CustomRadio = React.forwardRef<HTMLDivElement, CustomRadioProps>(
  (
    {
      checked = false,
      onChange,
      trackClassName,
      knobClassName,
      disabled = false,
      name,
      value,
      id,
      ...props
    },
    ref,
  ) => {
    const handleClick = () => {
      if (!disabled && onChange) {
        onChange(!checked);
      }
    };

    return (
      <div
        ref={ref}
        role='radio'
        aria-checked={checked}
        aria-disabled={disabled}
        tabIndex={disabled ? -1 : 0}
        onClick={handleClick}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleClick();
          }
        }}
        className={cn(
          'w-10 h-5 rounded-full relative cursor-pointer transition-colors',
          disabled && 'cursor-not-allowed opacity-50',
          checked ? trackClassName || 'bg-[#DB783E]' : 'bg-gray-600/30',
          props.className,
        )}
        {...props}
      >
        <div
          className={cn(
            'absolute w-3 h-3 rounded-full transition-all duration-200 ease-in-out',
            checked ? 'right-1' : 'left-1',
            'top-1',
            checked ? knobClassName || 'bg-[#DB783E33]/20' : 'bg-gray-400',
          )}
        />
        {name && (
          <input
            type='radio'
            name={name}
            value={value}
            checked={checked}
            onChange={() => {}}
            className='sr-only'
            id={id}
          />
        )}
      </div>
    );
  },
);

CustomRadio.displayName = 'CustomRadio';
