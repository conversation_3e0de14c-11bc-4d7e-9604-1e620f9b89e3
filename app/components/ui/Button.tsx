import * as React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 py-3 whitespace-nowrap rounded-lg text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
  {
    variants: {
      variant: {
        default:
          'bg-[#AA423A] hover:bg-[#AA423A95] text-white cursor-pointer shadow-xs',
        destructive:
          'bg-[#161617] border border-[#313133] text-white/70 cursor-pointer shadow-xs',
        outline:
          'border cursor-pointer bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',
        secondary:
          'bg-secondary cursor-pointer text-secondary-foreground shadow-xs hover:bg-secondary/80',
        ghost:
          'hover:bg-accent cursor-pointer hover:text-accent-foreground dark:hover:bg-accent/50',
        link: 'text-primary underline-offset-4 hover:underline',
      },
      size: {
        default: 'h-9 px-4 py-3 has-[>svg]:px-3',
        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',
        lg: 'h-10 rounded-lg px-6 has-[>svg]:px-4',
        icon: 'size-9 p-0',
      },
      fullWidth: {
        true: 'w-full',
      },
      loading: {
        true: '',
      },
      loadingPosition: {
        start: '',
        center: '',
        end: '',
      },
    },
    compoundVariants: [
      {
        loading: true,
        loadingPosition: 'center',
        class: 'text-transparent',
      },
      {
        loading: true,
        loadingPosition: 'start',
        class: 'relative',
      },
      {
        loading: true,
        loadingPosition: 'end',
        class: 'relative',
      },
    ],
    defaultVariants: {
      variant: 'default',
      size: 'default',
      fullWidth: false,
      loading: false,
      loadingPosition: 'center',
    },
  },
);

interface ButtonProps
  extends React.ComponentProps<'button'>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  loading?: boolean;
  loadingIndicator?: React.ReactNode;
  fullWidth?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant,
      size,
      asChild = false,
      startIcon,
      endIcon,
      loading = false,
      loadingIndicator,
      loadingPosition = 'center',
      fullWidth = false,
      disabled = false,
      children,
      ...props
    },
    ref,
  ) => {
    const Comp = asChild ? Slot : 'button';
    const isIconOnly = size === 'icon' && !children && (startIcon || endIcon);

    // Default loading indicator (a simple spinner)
    const defaultLoadingIndicator = (
      <svg
        className='animate-spin size-4 text-current'
        xmlns='http://www.w3.org/2000/svg'
        fill='none'
        viewBox='0 0 24 24'
      >
        <circle
          className='opacity-25'
          cx='12'
          cy='12'
          r='10'
          stroke='currentColor'
          strokeWidth='4'
        />
        <path
          className='opacity-75'
          fill='currentColor'
          d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z'
        />
      </svg>
    );

    const effectiveLoadingIndicator =
      loadingIndicator || defaultLoadingIndicator;

    const content = (
      <>
        {startIcon && !isIconOnly && (
          <span
            className={cn(
              'mr-2 flex items-center',
              loading && loadingPosition === 'start' && 'opacity-0',
              size === 'sm' && 'mr-1',
            )}
          >
            {startIcon}
          </span>
        )}

        {loading && loadingPosition === 'start' && (
          <span
            className={cn(
              'absolute left-3 flex items-center',
              size === 'sm' && 'left-2',
              size === 'lg' && 'left-4',
            )}
          >
            {effectiveLoadingIndicator}
          </span>
        )}

        {children}

        {loading && loadingPosition === 'center' && (
          <span className='absolute flex items-center'>
            {effectiveLoadingIndicator}
          </span>
        )}

        {endIcon && !isIconOnly && (
          <span
            className={cn(
              'ml-2 flex items-center',
              loading && loadingPosition === 'end' && 'opacity-0',
              size === 'sm' && 'ml-1',
            )}
          >
            {endIcon}
          </span>
        )}

        {loading && loadingPosition === 'end' && (
          <span
            className={cn(
              'absolute right-3 flex items-center',
              size === 'sm' && 'right-2',
              size === 'lg' && 'right-4',
            )}
          >
            {effectiveLoadingIndicator}
          </span>
        )}

        {isIconOnly && (startIcon || endIcon)}
      </>
    );

    return (
      <Comp
        data-slot='button'
        className={cn(
          buttonVariants({
            variant,
            size,
            fullWidth,
            loading,
            loadingPosition,
            className,
          }),
          isIconOnly && 'justify-center',
        )}
        disabled={disabled || loading}
        ref={ref}
        {...props}
      >
        {asChild ? children : content}
      </Comp>
    );
  },
);

Button.displayName = 'Button';

export { Button, buttonVariants };
