'use client';
import { cn } from '@/app/lib/utils';
import React from 'react';

// InputAdornmentProps for the adornment component
export interface InputAdornmentProps
  extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  position: 'start' | 'end';
  disablePointerEvents?: boolean;
  disableTypography?: boolean;
  className?: string;
  hasBg?: boolean;
}

// InputProps for the input component
export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  startAdornment?: React.ReactNode;
  endAdornment?: React.ReactNode;
  className?: string;
}

// InputAdornment component
const InputAdornment = React.forwardRef<HTMLDivElement, InputAdornmentProps>(
  (
    {
      children,
      position,
      disablePointerEvents = false,
      disableTypography = false,
      hasBg = true,
      className,
      ...props
    },
    ref,
  ) => {
    const adornmentContent =
      typeof children === 'string' && !disableTypography ? (
        <span className='text-[#8E9196] text-sm'>{children}</span>
      ) : (
        children
      );

    return (
      <div
        ref={ref}
        className={cn(
          'flex items-center h-10 w-full  px-3',
          position === 'start' && hasBg
            ? 'border-r  rounded-l-lg  border-[#313133]'
            : '',
          position === 'end' && hasBg
            ? 'border-l border-[#313133] rounded-l-lg'
            : '',
          disablePointerEvents && 'pointer-events-none',
          hasBg ? 'border-[#313133] bg-[#313133]' : '',
          'text-[#8E9196]',
          className,
        )}
        {...props}
      >
        {position === 'start' && (
          <span className='notranslate' aria-hidden>
            ​
          </span>
        )}
        {adornmentContent}
        {position === 'end' && (
          <span className='notranslate' aria-hidden>
            ​
          </span>
        )}
      </div>
    );
  },
);
InputAdornment.displayName = 'InputAdornment';

// Input component
const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, startAdornment, endAdornment, ...props }, ref) => {
    return (
      <div className='relative flex items-center w-full'>
        {startAdornment && (
          <div className='absolute left-0 flex items-center h-full'>
            {React.isValidElement(startAdornment) ? (
              startAdornment
            ) : (
              <InputAdornment position='start'>{startAdornment}</InputAdornment>
            )}
          </div>
        )}
        <input
          type={type}
          className={cn(
            'flex h-10 w-full rounded-md border border-[#313133] bg-background py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring/20 focus-visible:ring-offset-0 disabled:cursor-not-allowed disabled:opacity-50',
            startAdornment ? 'pl-10' : 'pl-3',
            endAdornment ? 'pr-10' : 'pr-3',
            className,
          )}
          ref={ref}
          {...props}
        />
        {endAdornment && (
          <div className='absolute right-0 flex items-center h-full'>
            {React.isValidElement(endAdornment) ? (
              endAdornment
            ) : (
              <InputAdornment position='end'>{endAdornment}</InputAdornment>
            )}
          </div>
        )}
      </div>
    );
  },
);
Input.displayName = 'Input';

export { Input, InputAdornment };
