'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface CustomRangeProps {
  min?: number;
  max?: number;
  value: number;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  className?: string;
  fillColor?: string;
  trackColor?: string;
  disabled?: boolean;
  trackHeight?: string;
}

const CustomRange: React.FC<CustomRangeProps> = ({
  min = 0,
  max = 100,
  value,
  onChange,
  className,
  fillColor = '#AA423A',
  trackColor = '#313133',
  disabled = false,
  trackHeight = '2px', // Default to 2px for a thinner track
}) => {
  // Calculate percentage for gradient
  const percentage = ((value - min) / (max - min)) * 100;

  return (
    <div className={cn('relative w-full flex items-center', className)}>
      <input
        type='range'
        min={min}
        max={max}
        value={value}
        onChange={onChange}
        disabled={disabled}
        className={cn(
          'w-full rounded-lg appearance-none cursor-pointer',
          'focus:outline-none focus:ring-0',
          'range-input',
          disabled && 'opacity-50 cursor-not-allowed',
        )}
        style={{
          height: trackHeight,
          background: `linear-gradient(to right, ${fillColor} 0%, ${fillColor} ${percentage}%, ${trackColor} ${percentage}%, ${trackColor} 100%)`,
        }}
      />
      <style jsx global>{`
        .range-input::-webkit-slider-thumb {
          -webkit-appearance: none;
          appearance: none;
          width: 14px;
          height: 14px;
          border-radius: 50%;
          background: white;
          border: 1px solid #e0e0e0;
          cursor: pointer;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          margin-top: -3px; /* Adjust to center the thumb on the thinner track */
        }

        .range-input::-moz-range-thumb {
          width: 14px;
          height: 14px;
          border-radius: 50%;
          background: white;
          border: 1px solid #e0e0e0;
          cursor: pointer;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .range-input::-moz-range-track {
          height: ${trackHeight};
          border-radius: 4px;
        }

        .range-input:disabled::-webkit-slider-thumb {
          cursor: not-allowed;
        }

        .range-input:disabled::-moz-range-thumb {
          cursor: not-allowed;
        }
      `}</style>
    </div>
  );
};

export { CustomRange };
