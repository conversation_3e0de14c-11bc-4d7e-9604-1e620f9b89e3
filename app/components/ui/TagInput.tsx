'use client';
import { cn } from '@/app/lib/utils';
import React, { useState, useRef, KeyboardEvent, ClipboardEvent } from 'react';
import { X } from 'lucide-react';
import { InputAdornment, InputProps } from './Input'; // Import your existing Input component

export interface TagInputProps extends Omit<InputProps, 'value' | 'onChange'> {
  value: string[];
  onChange: (value: string[]) => void;
  onInputChange?: (value: string) => void;
  validator?: (value: string) => boolean;
  max?: number;
  tagClassName?: string;
  tagContainerClassName?: string;
  delimiter?: string | RegExp;
  allowPaste?: boolean;
  renderTag?: (tag: string, handleDelete: () => void) => React.ReactNode;
}

const TagInput = React.forwardRef<HTMLInputElement, TagInputProps>(
  (
    {
      className,
      tagClassName,
      tagContainerClassName,
      placeholder = 'Add tag...',
      value = [],
      onChange,
      onInputChange,
      validator = () => true,
      max,
      delimiter = /[,\s]/,
      allowPaste = true,
      renderTag,
      startAdornment,
      endAdornment,
      ...props
    },
    ref,
  ) => {
    const [inputValue, setInputValue] = useState('');
    const inputRef = useRef<HTMLInputElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);

    const addTag = (tag: string) => {
      const trimmedTag = tag.trim();
      if (
        trimmedTag &&
        !value.includes(trimmedTag) &&
        validator(trimmedTag) &&
        (!max || value.length < max)
      ) {
        onChange([...value, trimmedTag]);
        return true;
      }
      return false;
    };

    const removeTag = (index: number) => {
      const newTags = [...value];
      newTags.splice(index, 1);
      onChange(newTags);
    };

    const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
      if (!inputValue && e.key === 'Backspace' && value.length > 0) {
        // Remove the last tag if backspace is pressed and input is empty
        removeTag(value.length - 1);
      } else if (e.key === 'Enter' && inputValue) {
        e.preventDefault();
        if (addTag(inputValue)) {
          setInputValue('');
        }
      }
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value;
      setInputValue(newValue);
      if (onInputChange) {
        onInputChange(newValue);
      }

      // Check if delimiter is typed
      const lastChar = newValue.charAt(newValue.length - 1);
      if (
        (typeof delimiter === 'string' && lastChar === delimiter) ||
        (delimiter instanceof RegExp && delimiter.test(lastChar))
      ) {
        const tagValue = newValue.slice(0, -1).trim();
        if (addTag(tagValue)) {
          setInputValue('');
        }
      }
    };

    const handlePaste = (e: ClipboardEvent<HTMLInputElement>) => {
      if (!allowPaste) return;

      e.preventDefault();
      const pastedText = e.clipboardData.getData('text');
      const tags = pastedText
        .split(delimiter)
        .map((tag) => tag.trim())
        .filter((tag) => tag && !value.includes(tag) && validator(tag));

      if (tags.length > 0) {
        const newTags = [...value];
        tags.forEach((tag) => {
          if (!max || newTags.length < max) {
            newTags.push(tag);
          }
        });
        onChange(newTags);
      }
    };

    const focusInput = () => {
      if (inputRef.current) {
        inputRef.current.focus();
      }
    };

    return (
      <div
        className={cn(
          'flex items-center w-full min-h-10 rounded-md border border-[#313133] bg-background py-1.5 px-3 text-sm focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2',
          className,
        )}
        onClick={focusInput}
        ref={containerRef}
      >
        {startAdornment && (
          <div className='mr-2'>
            {React.isValidElement(startAdornment) ? (
              startAdornment
            ) : (
              <InputAdornment position='start' hasBg={false}>
                {startAdornment}
              </InputAdornment>
            )}
          </div>
        )}

        <div
          className={cn(
            'flex flex-wrap gap-1.5 flex-grow items-center',
            tagContainerClassName,
          )}
        >
          {value.map((tag, index) => {
            const handleDelete = () => removeTag(index);

            if (renderTag) {
              return (
                <React.Fragment key={`${tag}-${index}`}>
                  {renderTag(tag, handleDelete)}
                </React.Fragment>
              );
            }

            return (
              <div
                key={`${tag}-${index}`}
                className={cn(
                  'inline-flex items-center gap-1 bg-[#313133] text-white px-2 py-1 rounded-md text-sm max-w-full',
                  tagClassName,
                )}
              >
                <span className='truncate'>{tag}</span>
                <button
                  type='button'
                  onClick={(e) => {
                    e.stopPropagation();
                    removeTag(index);
                  }}
                  className='text-white/70 hover:text-white flex-shrink-0'
                >
                  <X className='h-3 w-3' />
                </button>
              </div>
            );
          })}

          <input
            ref={(node) => {
              // Handle both the forwarded ref and our local ref
              if (typeof ref === 'function') {
                ref(node);
              } else if (ref) {
                ref.current = node;
              }
              inputRef.current = node;
            }}
            type='text'
            className='flex-grow min-w-[120px] bg-transparent outline-none border-none p-1 text-sm placeholder:text-muted-foreground focus:outline-none h-8'
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onPaste={handlePaste}
            placeholder={value.length === 0 ? placeholder : ''}
            disabled={max !== undefined && value.length >= max}
            {...props}
          />
        </div>

        {endAdornment && (
          <div className='ml-2'>
            {React.isValidElement(endAdornment) ? (
              endAdornment
            ) : (
              <InputAdornment position='end' hasBg={false}>
                {endAdornment}
              </InputAdornment>
            )}
          </div>
        )}
      </div>
    );
  },
);

TagInput.displayName = 'TagInput';

export { TagInput };
