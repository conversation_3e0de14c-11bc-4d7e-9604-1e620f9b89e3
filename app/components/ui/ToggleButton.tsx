import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/app/components/ui/Button';
import { cn } from '@/lib/utils';

interface ToggleButtonProps {
  options: { id: string; label: string }[];
  value: string;
  onChange: (value: string) => void;
  className?: string;
}

export const ToggleButton = ({
  options,
  value,
  onChange,
  className,
}: ToggleButtonProps) => {
  const [activeButtonRef, setActiveButtonRef] =
    useState<HTMLButtonElement | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [indicatorStyle, setIndicatorStyle] = useState({
    width: 0,
    left: 0,
    transition: 'none',
  });

  useEffect(() => {
    if (activeButtonRef && containerRef.current) {
      // Get the position of the active button relative to the container
      const containerRect = containerRef.current.getBoundingClientRect();
      const buttonRect = activeButtonRef.getBoundingClientRect();

      setIndicatorStyle({
        width: buttonRect.width,
        left: buttonRect.left - containerRect.left,
        transition: 'all 0.3s ease',
      });
    }
  }, [activeButtonRef, value]);

  return (
    <div
      ref={containerRef}
      className={cn(
        'relative inline-flex rounded-xl p-1 border border-[#313133] bg-[#161617]',
        className,
      )}
    >
      {/* Background indicator that slides */}
      <div
        className='absolute bg-[#AA423A] rounded-xl transition-all duration-300 ease-in-out'
        style={{
          width: `${indicatorStyle.width}px`,
          left: `${indicatorStyle.left}px`,
          height: 'calc(100% - 8px)',
          top: '4px',
          zIndex: 0,
        }}
      />

      {options.map((option) => (
        <Button
          key={option.id}
          type='button'
          onClick={() => onChange(option.id)}
          variant='ghost'
          ref={option.id === value ? setActiveButtonRef : null}
          className={`
            relative z-10 px-4 py-1 text-sm rounded-xl transition-colors duration-200 focus:outline-none
            ${
              option.id === value
                ? 'text-white'
                : 'text-white/70 hover:text-white'
            }
          `}
        >
          {option.label}
        </Button>
      ))}
    </div>
  );
};
