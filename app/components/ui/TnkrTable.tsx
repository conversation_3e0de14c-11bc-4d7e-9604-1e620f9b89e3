import React, { useState, useMemo } from 'react';
import { Input, InputAdornment } from '@/app/components/ui/Input';
import { Search } from 'lucide-react';
import { Button } from '@/app/components/ui/Button';
import { cn } from '@/app/lib/utils';

export interface TableColumn<T> {
  header: string;
  render: (row: T) => React.ReactNode;
}

export interface TnkrTableProps<T> {
  /** Column definitions */
  columns: TableColumn<T>[];
  /** Row data */
  data: T[];
  /** Toggle search bar */
  showSearch?: boolean;
  /** Placeholder for search input */
  searchPlaceholder?: string;
  /** Toggle action button */
  showAction?: boolean;
  /** Label for action button */
  actionLabel?: string;
  /** Callback when action button is clicked */
  onActionClick?: () => void;
  /** Loading state */
  isLoading?: boolean;
  /** Remove left and right borders */
  noBorder?: boolean;
}

/**
 * A table with optional search and action button.
 * Filters rows by a simple text match on the full row JSON.
 */
function TnkrTable<T extends object>({
  columns,
  data,
  showSearch = false,
  searchPlaceholder = 'Search...',
  showAction = false,
  actionLabel = 'Add',
  onActionClick,
  isLoading = false,
  noBorder,
}: TnkrTableProps<T>) {
  const [searchTerm, setSearchTerm] = useState('');

  const filteredData = useMemo(() => {
    if (!showSearch || !searchTerm) return data;
    const lower = searchTerm.toLowerCase();
    return data.filter((row) =>
      JSON.stringify(row).toLowerCase().includes(lower),
    );
  }, [data, searchTerm, showSearch]);

  const renderSkeleton = () => (
    <tbody>
      {[...Array(5)].map((_, rowIndex) => (
        <tr
          key={`skeleton-${rowIndex}`}
          className='border-t border-[#313133] animate-pulse'
        >
          {columns.map((_, colIndex) => (
            <td key={`skeleton-cell-${colIndex}`} className='p-3'>
              <div className='h-10 bg-[#313133]/50 rounded'></div>
            </td>
          ))}
        </tr>
      ))}
    </tbody>
  );

  return (
    <div className='relative'>
      {/* Controls: Search & Action */}
      {(showSearch || showAction) && (
        <div className='absolute max-w-sm flex right-0 -top-18 p-4'>
          <div className='flex items-center gap-4'>
            {showSearch && (
              <Input
                type='text'
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder={searchPlaceholder}
                startAdornment={
                  <InputAdornment hasBg={false} position='start'>
                    <Search size={16} />
                  </InputAdornment>
                }
                className='border rounded-lg py-2 text-sm focus:outline-none max-w-xs'
                disabled={isLoading}
              />
            )}
            {showAction && (
              <Button
                className='sm:w-auto bg-[#AA423A] hover:bg-[#AA423A95] text-white'
                variant='default'
                onClick={onActionClick}
                disabled={isLoading}
              >
                {actionLabel}
              </Button>
            )}
          </div>
        </div>
      )}

      <div
        className={cn(
          'rounded-lg',
          noBorder
            ? 'border-t border-b border-[#313133]'
            : 'border border-[#313133]',
        )}
      >
        <table className='w-full text-left'>
          <thead>
            <tr
              className={cn(
                'bg-[#313133]',
                noBorder && 'rounded-lg overflow-hidden',
              )}
            >
              {columns.map((col, idx) => (
                <th
                  key={idx}
                  className={cn(
                    'p-3 font-semibold text-gray-300',
                    noBorder && idx === 0 && 'rounded-l-lg',
                    noBorder && idx === columns.length - 1 && 'rounded-r-lg',
                  )}
                >
                  {col.header}
                </th>
              ))}
            </tr>
          </thead>
          {isLoading ? (
            renderSkeleton()
          ) : (
            <tbody>
              {(showSearch ? filteredData : data).map((row, rowIndex) => (
                <tr
                  key={rowIndex}
                  className='border-t border-[#313133] cursor-pointer hover:bg-[#313133]/70'
                >
                  {columns.map((col, colIndex) => (
                    <td key={colIndex} className='p-3'>
                      {col.render(row)}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          )}
        </table>
      </div>
    </div>
  );
}

export default TnkrTable;
