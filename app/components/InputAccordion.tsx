import React, { useState } from 'react';
import { Chevron } from '@/app/components/Chevron';
import { cn } from '@/lib/utils';

// Accordion component
interface AccordionProps {
  logo?: React.ReactNode;
  title: string;
  badge?: React.ReactNode;
  children?: React.ReactNode;
  disabled?: boolean;
  onClick?: () => void;
}
const InputAccordion: React.FC<AccordionProps> = ({
  logo,
  title,
  badge,
  children,
  disabled = false,
  onClick,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleClick = () => {
    if (disabled && onClick) {
      onClick();
      return;
    }
    if (onClick) {
      onClick();
    }
    setIsOpen((o) => !o);
  };

  return (
    <div
      className={cn('border py-2 px-2 rounded-lg', disabled && 'opacity-60')}
    >
      <div
        className={cn(
          'w-full flex items-center justify-between px-4 py-3',
          !disabled && 'cursor-pointer',
        )}
        {...{
          onClick: handleClick,
          role: 'button',
          tabIndex: 0,
        }}
      >
        <div className='flex items-center space-x-2'>
          <div className='flex items-center gap-2'>
            {/* Active selector circle with focus ring */}
            <div
              className={cn(
                'w-5 h-5 rounded-full transition-all mr-4 duration-200 flex items-center justify-center',
                isOpen
                  ? 'ring ring-offset-2 ring-white ring-opacity-50'
                  : 'border border-[#313133]',
                disabled && 'opacity-50',
              )}
            >
              <div className='w-2 h-2 rounded-full' />
            </div>

            {/* Logo */}
            {logo && logo}
          </div>
          <span className='text-white font-medium'>{title}</span>
          {badge && <span>{badge}</span>}
        </div>
        {!disabled && <Chevron isOpen={isOpen} />}
      </div>
      {isOpen && !disabled && (
        <div className='px-4 py-2 border-[#313133]'>{children}</div>
      )}
    </div>
  );
};

export { InputAccordion };
