'use client';
import React, { useState, useEffect, useRef } from 'react';

interface DeployedSitePreviewProps {
  url: string;
  height?: string | number;
  width?: string | number;
  className?: string;
}

const DeployedSitePreview: React.FC<DeployedSitePreviewProps> = ({
  url,
  height = 350,
  width = '100%',
  className = '',
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    setIsLoading(true);
    setHasError(false);

    // Clear previous timeout if it exists
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
    }

    // Set a timeout to handle cases where the iframe might not trigger load/error events
    loadingTimeoutRef.current = setTimeout(() => {
      if (isLoading) {
        console.error('Failed to load preview of site: timeout occurred');
        setHasError(true);
        setIsLoading(false);
      }
    }, 10000); // Increased timeout to 10 seconds

    return () => {
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
    };
  }, [url, isLoading]);

  const handleIframeLoad = () => {
    // Clear the timeout when iframe successfully loads
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
      loadingTimeoutRef.current = null;
    }
    setIsLoading(false);
  };

  const handleIframeError = () => {
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
      loadingTimeoutRef.current = null;
    }
    setHasError(true);
    setIsLoading(false);
    console.error(
      'Failed to load preview of site: iframe error event triggered',
    );
  };

  // Function to retry loading the iframe

  return (
    <div>
      <div className='top-2 left-2 z-10'>
        <div className='bg-[#024A43]/20 text-[#00B453] text-xs px-3 py-1 w-fit ml-4 rounded-tl-md rounded-tr-md flex items-center'>
          <div className='w-2 h-2 bg-[#00B453] rounded-full mr-1.5'></div>
          Live
        </div>
      </div>
      <div
        className={`relative rounded-xl ${className}`}
        style={{
          width: typeof width === 'number' ? `${width}px` : width,
          border: '0.5rem solid #161617',
        }}
      >
        <div
          style={{
            height: typeof height === 'number' ? `${height}px` : height,
            overflow: 'hidden',
            backgroundColor: '#9CA3AF',
            position: 'relative',
            borderRadius: '0.5rem',
          }}
        >
          {!hasError && (
            <>
              <iframe
                ref={iframeRef}
                src={url}
                style={{
                  width: '100%',
                  height: '100%',
                  border: '0',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                }}
                onLoad={handleIframeLoad}
                onError={handleIframeError}
                sandbox='allow-scripts allow-same-origin allow-forms'
              />
              {isLoading && (
                <div
                  style={{
                    position: 'absolute',
                    inset: 0,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: '#9CA3AF',
                    color: '#4B5563',
                  }}
                >
                  <div className='flex flex-col items-center'>
                    <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-[#AA423A] mb-4'></div>
                    <p>Loading preview...</p>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default DeployedSitePreview;
