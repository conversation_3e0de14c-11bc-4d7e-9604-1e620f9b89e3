import React, { useEffect, useRef } from 'react';
import { Button } from '@/app/components/ui/Button';
import { X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ModalContainerProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  closeOnOutsideClick?: boolean;
}

const modalSizes = {
  sm: 'max-w-sm',
  md: 'max-w-md',
  lg: 'max-w-lg',
  xl: 'max-w-xl',
  full: 'max-w-[90%]',
};

const ModalContainer: React.FC<ModalContainerProps> = ({
  isOpen,
  onClose,
  children,
  className,
  size = 'md',
  closeOnOutsideClick = true,
}) => {
  const modalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!isOpen || !closeOnOutsideClick) return;

    const handleClickOutside = (event: MouseEvent) => {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    // Add the event listener when the modal is open
    document.addEventListener('mousedown', handleClickOutside);

    // Clean up the event listener when the component unmounts or modal closes
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose, closeOnOutsideClick]);

  if (!isOpen) return null;

  return (
    <div className='fixed inset-0 z-[9999] flex items-center justify-center bg-background/80 backdrop-blur-sm'>
      <div
        ref={modalRef}
        className={cn(
          'relative rounded-3xl bg-[#161617] border border-[#313133] dark:bg-[#161617] shadow-lg w-full mx-4',
          modalSizes[size],
          className,
        )}
      >
        <Button
          variant='ghost'
          size='icon'
          onClick={onClose}
          aria-label='Close modal'
          className='absolute top-3 right-3 text-[#AA423A] hover:text-[#AA423A] dark:text-[#AA423A] dark:hover:text-[#AA423A]'
        >
          <X className='h-4 w-4' />
        </Button>
        {children}
      </div>
    </div>
  );
};

interface ModalBodyProps {
  icon?: React.ReactNode;
  title?: string;
  description?: string;
  children?: React.ReactNode;
}

const ModalBody: React.FC<ModalBodyProps> = ({
  title,
  description,
  icon,
  children,
}) => (
  <div className='px-6 py-4'>
    <div className='flex items-start gap-3'>
      {icon && <div className='flex-shrink-0 mt-1'>{icon}</div>}
      <div className='flex-1'>
        {title && (
          <h2 className='text-xl font-normal dark:text-white'>{title}</h2>
        )}
        {description && <p className='text-white/70 mt-2'>{description}</p>}
        {children}
      </div>
    </div>
  </div>
);

interface ModalFooterProps {
  onConfirm?: () => void;
  confirmLabel?: string;
  onCancel?: () => void;
  cancelLabel?: string;
  showCancel?: boolean;
  disabled?: boolean;
}

const ModalFooter: React.FC<ModalFooterProps> = ({
  onConfirm,
  confirmLabel = 'Confirm',
  onCancel,
  cancelLabel = 'Cancel',
  showCancel = true,
  disabled = false,
}) => {
  if (!onConfirm && !onCancel) return null;
  return (
    <div className='flex justify-start px-6 space-x-2 py-4'>
      {showCancel && onCancel && (
        <Button
          className='cursor-pointer'
          variant='outline'
          onClick={onCancel}
          disabled={disabled}
        >
          {cancelLabel}
        </Button>
      )}
      {onConfirm && (
        <Button
          variant='default'
          className='sm:w-auto bg-[#AA423A] hover:bg-[#AA423A95] text-white'
          onClick={onConfirm}
          disabled={disabled}
        >
          {confirmLabel}
        </Button>
      )}
    </div>
  );
};

export { ModalContainer, ModalBody, ModalFooter };
