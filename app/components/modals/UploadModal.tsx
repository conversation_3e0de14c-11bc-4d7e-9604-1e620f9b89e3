'use client';

import React, { useState, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ModalFooter } from './Modal';
import { Check, Trash2 } from 'lucide-react';
import UploadCloudSvg from '@/assets/icons/upload_cloud.svg';
import { Icon } from '@/app/components/CustomIcon';
import { Button } from '@/app/components/ui/Button';

interface FileInfo {
  name: string;
  size: number;
  type: string;
}

interface UploadModalProps {
  isOpen: boolean;
  modalTitle: string;
  onClose: () => void;
  onUpload: (file: File) => void;
  supportedFormats?: string[];
  children?: React.ReactNode;
  footerContent?: React.ReactNode;
}

export const UploadModal: React.FC<UploadModalProps> = ({
  isOpen,
  onClose,
  modalTitle,
  onUpload,
  supportedFormats = ['STP', 'STEP', 'ASM', 'CDX', 'PDF'],
  children,
  footerContent,
}) => {
  const [file, setFile] = useState<File | null>(null);
  const [fileInfo, setFileInfo] = useState<FileInfo | null>(null);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const droppedFile = e.dataTransfer.files[0];
      handleFileSelection(droppedFile);
    }
  };

  const handleFileSelection = (selectedFile: File) => {
    // Extract file extension
    const extension = selectedFile.name.split('.').pop()?.toUpperCase() || '';

    // Check if the file format is supported
    if (!supportedFormats.includes(extension)) {
      // Show error or alert for unsupported format
      console.error('Unsupported file format');
      return;
    }

    setFile(selectedFile);
    onUpload(selectedFile);
    setFileInfo({
      name: selectedFile.name,
      size: selectedFile.size,
      type: selectedFile.type,
    });
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      handleFileSelection(e.target.files[0]);
    }
  };

  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const removeFile = () => {
    setFile(null);
    setFileInfo(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleUpload = () => {
    if (file) {
      onUpload(file);
      resetForm();
    }
  };

  const resetForm = () => {
    setFile(null);
    setFileInfo(null);
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B';
    else if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    else if (bytes < 1024 * 1024 * 1024)
      return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    return (bytes / (1024 * 1024 * 1024)).toFixed(1) + ' GB';
  };

  return (
    <ModalContainer isOpen={isOpen} onClose={onClose} size='xl'>
      <div className='py-1'>
        <div className='mb-6 border-b py-3 text-base border-b-[#313133] px-6'>
          <h3 className='text-xl font-medium text-white'>New {modalTitle}</h3>
        </div>

        {/* File Upload Area */}
        <div className='px-6'>
          {!fileInfo ? (
            <div
              className={` bg-[#242425] rounded-lg p-8 mb-6 flex flex-col items-center justify-center cursor-pointer transition-colors ${
                isDragging ? 'bg-[#AA423A]/5' : ''
              }`}
              style={{
                // borderColor: isDragging ? '#AA423A' : '#ffffff',
                backgroundImage: isDragging
                  ? 'linear-gradient(to left, #AA423A 50%, transparent 50%), linear-gradient(to left, #AA423A 50%, transparent 50%), linear-gradient(to top, #AA423A 50%, transparent 50%), linear-gradient(to top, #AA423A 50%, transparent 50%)'
                  : 'linear-gradient(to left, #ffffff 50%, transparent 50%), linear-gradient(to left, #ffffff 50%, transparent 50%), linear-gradient(to top, #ffffff 50%, transparent 50%), linear-gradient(to top, #ffffff 50%, transparent 50%)',
                backgroundPosition:
                  'left top, left bottom, left top, right top',
                backgroundRepeat: 'repeat-x, repeat-x, repeat-y, repeat-y',
                backgroundSize:
                  '19.5px 1px, 19.5px 1px, 1px 19.5px, 1px 19.5px',
              }}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={triggerFileInput}
            >
              <input
                type='file'
                ref={fileInputRef}
                onChange={handleFileInputChange}
                accept={supportedFormats
                  .map((format) => `.${format.toLowerCase()}`)
                  .join(',')}
                className='hidden'
              />
              <div className='text-white/70 mb-2'>
                <Icon component={UploadCloudSvg} size={50} />
              </div>
              <p className='text-white/70 text-center mb-2'>
                Drag and drop or select a file to upload
              </p>
              <p className='text-white/70 text-sm text-center'>
                Supported formats {supportedFormats.join(', ')}
              </p>
            </div>
          ) : (
            <div
              className={` bg-[#242425] rounded-lg py-10 px-4 mb-6`}
              style={{
                backgroundImage:
                  'linear-gradient(to left, #ffffff 50%, transparent 50%), linear-gradient(to left, #ffffff 50%, transparent 50%), linear-gradient(to top, #ffffff 50%, transparent 50%), linear-gradient(to top, #ffffff 50%, transparent 50%)',
                backgroundPosition:
                  'left top, left bottom, left top, right top',
                backgroundRepeat: 'repeat-x, repeat-x, repeat-y, repeat-y',
                backgroundSize:
                  '19.5px 1px, 19.5px 1px, 1px 19.5px, 1px 19.5px',
              }}
            >
              <div className='flex items-center justify-between'>
                <div className='flex items-center'>
                  <div className='bg-[#AA423A] rounded-full p-1 mr-3'>
                    <Check className='h-5 w-5 text-[#242425]' />
                  </div>
                  <div>
                    <p className='text-white'>{fileInfo.name}</p>
                    <p className='text-gray-500 text-sm'>
                      {formatFileSize(fileInfo.size)}
                    </p>
                  </div>
                </div>
                <Button
                  size='icon'
                  variant='ghost'
                  startIcon={<Trash2 className='h-5 w-5' />}
                  onClick={(e) => {
                    e.stopPropagation();
                    removeFile();
                  }}
                  className='text-gray-400 hover:text-white'
                ></Button>
              </div>
            </div>
          )}

          {/* Children components (product name, hierarchy adjustment, etc.) */}
          {children}
        </div>

        {/* Custom Footer or Default Footer */}
        {footerContent ? (
          footerContent
        ) : (
          <ModalFooter
            onCancel={onClose}
            onConfirm={handleUpload}
            cancelLabel='Cancel'
            confirmLabel='Upload'
            showCancel={true}
            disabled={!file}
          />
        )}
      </div>
    </ModalContainer>
  );
};
