// File: components/layouts/editor/EditorTabPanel.tsx
import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { TabbedMenu } from '@/app/components/TabbedMenu';
import { EditorTabPanelProps } from './types';

export const EditorTabPanel: React.FC<EditorTabPanelProps> = ({
  tabs,
  defaultTabId,
  onChange,
  className,
}) => {
  // Get initial tab ID
  const initialTabId = defaultTabId || (tabs.length > 0 ? tabs[0].id : '');
  const [activeTabId, setActiveTabId] = useState<string>(initialTabId);

  // Actively render content for each tab, but hide inactive ones
  // This approach ensures that tab content is properly rendered when switching tabs
  // regardless of how TabbedMenu works internally
  const renderAllTabs = () => {
    return tabs.map((tab) => {
      const isActive = tab.id === activeTabId;

      return (
        <div
          key={tab.id}
          className={cn(
            'flex-1 overflow-auto w-full h-full',
            isActive ? 'block' : 'hidden',
          )}
        >
          {tab.panels.map((panel) => {
            const PanelComponent = panel.component;
            return (
              <PanelComponent
                key={panel.id}
                id={panel.id}
                title={panel.title}
                {...(panel.props || {})}
              />
            );
          })}
        </div>
      );
    });
  };

  // Handle tab change
  const handleTabChange = (tabId: string) => {
    console.log('Tab changed to:', tabId); // Debug log
    setActiveTabId(tabId);
    if (onChange) {
      onChange(tabId);
    }
  };

  // Transform tabs for TabbedMenu
  const tabbedMenuTabs = tabs.map((tab) => ({
    id: tab.id,
    label: tab.label,
    // We're handling content separately, so just pass null
    content: null,
  }));

  return (
    <div className={cn('flex flex-col h-full', className)}>
      {/* Wrapper to ensure TabbedMenu works with our layout */}
      <div className='flex flex-col h-full text-sm'>
        {/* Tab Navigation */}
        <TabbedMenu
          tabs={tabbedMenuTabs}
          onChange={handleTabChange}
          defaultTabId={initialTabId}
          variant='underline'
        />

        {/* Tab Content - render all but only show active */}
        <div className='flex-1 overflow-hidden relative'>{renderAllTabs()}</div>
      </div>
    </div>
  );
};

export default EditorTabPanel;
