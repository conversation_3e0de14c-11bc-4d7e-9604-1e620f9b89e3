import { PartCategory, PointerStyle, Part, InteractiveModePart } from '../types';
import { RefObject } from 'react';

export type ModelControlFunctions = {
  rotate: (x: number, y: number) => void;
  pan: (x: number, y: number) => void;
  zoom: (factor: number) => void;
  reset: () => void;
};

export type PartOperations = {
  showPart: (partName: string) => void;
  hidePart: (partName: string) => void;
  togglePart: (partName: string) => void;
  highlightPart: (partName: string, color?: string) => void;
  removeHighlight: (partName: string) => void;
  setPartAttribute: (
    partName: string,
    attribute: string,
    value: string,
  ) => void;
  addShapeClickHandlers: () => void;
};

export interface ControlFunctions {
  toggleElement: (id: string, show?: boolean) => void;
  getElementById: (id: string) => Element | null;
  getElementsByClassName: (className: string) => NodeListOf<Element>;
  updateElementAttribute: (
    id: string,
    attribute: string,
    value: string,
  ) => void;
  updateElementContent: (id: string, content: string) => void;
  addEventListenerToElement: (
    id: string,
    event: string,
    handler: EventListener,
  ) => (() => void) | undefined;
  container: HTMLDivElement | null;
  getPartsList: () => Part[];
  partOperations: PartOperations;
}

export interface ModelViewCoreProps {
  htmlString: string;
  className?: string;
  style?: React.CSSProperties;
  onElementsReady?: (functions: ControlFunctions) => void;
}

export type SelectedTool = {
  mainTool: string;
  subTool?: string;
};

export type ExtendedPart = Omit<Part, 'category'> & {
  rank: number;
  visible: boolean;
  category: PartCategory;
  specifications: {
    type: string;
    [key: string]: any;
  };
};

export type ContextMenuState = {
  isOpen: boolean;
  position: { x: number; y: number };
  partName: string;
};

export type ModelViewCoreContextType = {
  // Model state
  partsList: ExtendedPart[];
  setPartsList: React.Dispatch<React.SetStateAction<ExtendedPart[]>>;
  controlFunctions: ModelControlFunctions | null;
  setControlFunctions: React.Dispatch<
    React.SetStateAction<ModelControlFunctions | null>
  >;
  selectedPart: string | null;
  setSelectedPart: React.Dispatch<React.SetStateAction<string | null>>;
  containerRef: RefObject<HTMLDivElement | null>;
  scriptsLoaded: boolean;
  isLoaded: boolean;
  initializeModel: (url: string) => Promise<ExtendedPart[]>;

  // Tool and pointer state
  selectedTool: SelectedTool | null;
  setSelectedTool: (tool: SelectedTool) => void;
  currentPointerId: string | null;
  setCurrentPointerId: (id: string | null) => void;
  currentPointerStyle: PointerStyle | null;
  setCurrentPointerStyle: (style: PointerStyle | null) => void;
  updateCanvasCursor: () => void;

  // Context menu state
  contextMenu: ContextMenuState;
  setContextMenu: React.Dispatch<React.SetStateAction<ContextMenuState>>;

  // Part operations
  partOperations: PartOperations;
  handlePartIsolate: (partName: string) => void;
  handlePartHide: (partName: string) => void;
  handlePartHighlight: (partName: string) => void;
  handlePartDelete: (partName: string) => void;
  handleShowAllParts: () => void;
  isPartHidden: (partName: string) => boolean;
  isPartHighlighted: (partName: string) => boolean;
  isPartIsolated: (partName: string) => boolean;
};
