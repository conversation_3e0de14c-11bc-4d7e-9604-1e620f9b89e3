'use client';

import React, { useEffect } from 'react';
import { ModelViewCoreProps } from './types';
import { useModelViewCore } from './context/ModelViewCoreContext';

const ModelViewCore: React.FC<ModelViewCoreProps> = ({
                                                       htmlString,
                                                       className,
                                                       style,
                                                       onElementsReady,
                                                     }) => {
  const { containerRef, scriptsLoaded, initializeModel, controlFunctions } =
    useModelViewCore();

  // Initialize model only when htmlString changes and scripts are loaded
  useEffect(() => {
    if (!htmlString || !scriptsLoaded) return;

    initializeModel(htmlString);
  }, [htmlString, scriptsLoaded]); // Remove initializeModel from deps since it's stable

  // Notify parent component when control functions are ready
  useEffect(() => {
    if (!controlFunctions || !onElementsReady) return;

    onElementsReady(controlFunctions);
  }, [controlFunctions, onElementsReady]);

  return (
    <div
      className={`w-full h-full ${className || ''}`}
      style={style}
      ref={containerRef}
    />
  );
};

export default ModelViewCore;
