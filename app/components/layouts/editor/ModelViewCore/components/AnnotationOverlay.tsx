import React from 'react';
import { AnnotationLabel } from 'react-annotation';
import { ReactAnnotation } from '../../types';

interface AnnotationOverlayProps {
  annotations: ReactAnnotation[];
  onAnnotationUpdate: (id: string, updates: Partial<ReactAnnotation>) => void;
  onAnnotationRemove: (id: string) => void;
}

const AnnotationOverlay: React.FC<AnnotationOverlayProps> = ({
  annotations,
  onAnnotationUpdate,
  onAnnotationRemove,
}) => {
  const handleLabelClick = (annotation: ReactAnnotation) => {
    // Toggle edit mode
    onAnnotationUpdate(annotation.id, { editMode: !annotation.editMode });
  };

  const handleLabelChange = (annotation: ReactAnnotation, newText: string) => {
    onAnnotationUpdate(annotation.id, {
      note: {
        ...annotation.note,
        label: newText,
      },
      editMode: false,
    });
  };

  const handleTitleChange = (annotation: ReactAnnotation, newTitle: string) => {
    onAnnotationUpdate(annotation.id, {
      note: {
        ...annotation.note,
        title: newTitle,
      },
    });
  };

  return (
    <div className="annotation-overlay absolute inset-0 pointer-events-none">
      <svg
        width="100%"
        height="100%"
        className="absolute inset-0"
        style={{ pointerEvents: 'none' }}
      >
        {annotations.map((annotation) => (
          <g key={annotation.id}>
            <AnnotationLabel
              x={annotation.x}
              y={annotation.y}
              dy={annotation.dy}
              dx={annotation.dx}
              color={annotation.color}
              className={annotation.className || "show-bg"}
              editMode={annotation.editMode || false}
              note={{
                title: annotation.note.title,
                label: annotation.note.label,
                align: annotation.note.align,
                orientation: annotation.note.orientation || "topBottom",
                bgPadding: annotation.note.bgPadding || 20,
                padding: annotation.note.padding || 15,
                titleColor: annotation.note.titleColor || annotation.color,
                lineType: annotation.note.lineType,
              }}
              connector={{
                end: annotation.connector.end || "arrow",
                type: annotation.connector.type || "line",
              }}
              events={{
                onClick: () => handleLabelClick(annotation),
                onDoubleClick: () => handleLabelClick(annotation),
              }}
            />
            {/* Add a small delete button for each annotation */}
            <foreignObject
              x={annotation.x + annotation.dx + 10}
              y={annotation.y + annotation.dy - 10}
              width="20"
              height="20"
              style={{ pointerEvents: 'auto' }}
            >
              <button
                className="w-5 h-5 bg-red-500 text-white rounded-full text-xs hover:bg-red-600 flex items-center justify-center"
                onClick={() => onAnnotationRemove(annotation.id)}
                title="Remove annotation"
              >
                ×
              </button>
            </foreignObject>
          </g>
        ))}
      </svg>
      
      {/* Editable text inputs for annotations in edit mode */}
      {annotations
        .filter(annotation => annotation.editMode)
        .map((annotation) => (
          <div
            key={`edit-${annotation.id}`}
            className="absolute pointer-events-auto"
            style={{
              left: annotation.x + annotation.dx,
              top: annotation.y + annotation.dy,
              transform: 'translate(-50%, -50%)',
            }}
          >
            <div className="bg-white border border-gray-300 rounded p-2 shadow-lg">
              <input
                type="text"
                value={annotation.note.title}
                onChange={(e) => handleTitleChange(annotation, e.target.value)}
                className="w-full mb-2 p-1 border border-gray-200 rounded text-sm font-semibold"
                placeholder="Title"
              />
              <textarea
                value={annotation.note.label}
                onChange={(e) => handleLabelChange(annotation, e.target.value)}
                className="w-full p-1 border border-gray-200 rounded text-sm resize-none"
                rows={3}
                placeholder="Annotation text"
                onBlur={() => onAnnotationUpdate(annotation.id, { editMode: false })}
                autoFocus
              />
            </div>
          </div>
        ))}
    </div>
  );
};

export default AnnotationOverlay;
