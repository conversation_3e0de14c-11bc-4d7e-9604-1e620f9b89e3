.body {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-family: '<PERSON><PERSON>', sans-serif;
    background: linear-gradient(135deg, #3a3a3a, #4a4a4a);
    color: #e0e0e0;
    height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: hidden;
    width: 100%;
}

/* Header Section */
.header {
    box-sizing: border-box;
    margin: 0;
    padding: 20px 0;
    width: 100%;
    background: rgba(30, 30, 30, 0.95);
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
    flex-shrink: 0;
}

.header h1 {
    font-size: 32px;
    font-weight: 700;
    color: #ffffff;
    letter-spacing: 2px;
}


.modelViewerContainer {
    box-sizing: border-box;
    margin: 0;
    width: 100%;
    max-width: 1400px;
    background: rgba(30, 30, 30, 0.9);
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.18);
    display: flex;
    gap: 25px;
    flex: 1;
    height: calc(100vh - 80px);
    overflow: hidden;
    flex-direction: row;
    flex-wrap: nowrap;
    flex-direction: column;
    background: rgba(45, 45, 45, 0.9);
    border-radius: 10px;
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.5);
    min-height: 0;
}

.modelViewerContainer h2 {
    text-align: center;
    color: #ffffff;
    margin-bottom: 15px;
    font-size: 24px;
    font-weight: 700;
}

/* Model Viewer */
.modelViewer {
    box-sizing: border-box;
    margin: 0;
    flex: 1;
    width: 100%;
    border: 2px solid #555555;
    border-radius: 8px;
    background: #2b2b2b;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* Controls Panel */
.controls {
    box-sizing: border-box;
    margin: 0;
    position: absolute;
    top: 15px;
    left: 15px;
    background: rgba(50, 50, 50, 0.8);
    padding: 10px;
    border-radius: 8px;
    z-index: 1000;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    overflow-y: auto;
    max-height: 90vh;
}

.controls h3 {
    color: #f0f0f0;
    margin-bottom: 10px;
    font-size: 18px;
    font-weight: 500;
}

.componentList {
    box-sizing: border-box;
    margin: 0;
    max-height: 150px;
    overflow-y: auto;
    margin-top: 10px;
}

.componentItem {
    box-sizing: border-box;
    margin: 5px 0;
    padding: 8px 12px;
    background: #3a3a3a;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.3s, transform 0.3s;
    font-size: 14px;
}

.componentItem:hover {
    background: #505050;
    transform: translateX(5px);
}

.componentItem.selected {
    background: #007bff;
    color: #ffffff;
}

/* Loading Indicators */
.modelLoading {
    box-sizing: border-box;
    margin: 0;
    display: none;
    text-align: center;
    margin-top: 10px;
    font-style: italic;
    color: #aaaaaa;
    font-size: 16px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-family: Arial, sans-serif;
}

/* Buttons */
.button {
    box-sizing: border-box;
    margin: 5px 0;
    padding: 8px 16px;
    background: linear-gradient(45deg, #6a11cb, #2575fc);
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.3s, transform 0.2s;
    font-size: 14px;
    font-weight: 500;
}

.button:hover {
    background: linear-gradient(45deg, #2575fc, #6a11cb);
    transform: translateY(-2px);
}

.button.active {
    background: linear-gradient(45deg, #fc2525, #cb6a11);
}

/* Log Panel */
.logPanel {
    box-sizing: border-box;
    margin: 0;
    position: absolute;
    bottom: 15px;
    right: 15px;
    width: 250px;
    max-height: 200px;
    overflow-y: auto;
    background: rgba(0, 0, 0, 0.7);
    color: #fff;
    padding: 10px;
    border-radius: 8px;
    font-size: 13px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    z-index: 1100;
}

/* Annotation Styles */
.annotation {
    box-sizing: border-box;
    margin: 0;
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    color: #ffffff;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 12px;
    white-space: nowrap;
    pointer-events: auto;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    cursor: move;
}

.cardStyleRounded {
    border-radius: 10px;
    border: 2px solid #ffffff;
}

.cardStyleSquare {
    border-radius: 0;
    border: 1px dashed #cccccc;
}

.cardStyleShadow {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.7);
    border: none;
}

.annotationArrow {
    box-sizing: border-box;
    margin: 0;
    position: absolute;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid rgba(0, 0, 0, 0.8);
    pointer-events: none;
}

.arrowStyleLarge {
    border-left: 12px solid transparent;
    border-right: 12px solid transparent;
    border-top: 12px solid rgba(0, 0, 0, 0.8);
}

.arrowStyleSmall {
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid rgba(0, 0, 0, 0.8);
}

.arrowStyleOutline {
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid transparent;
    border-top-style: solid;
}

/* Customization Controls */
.customizationControl {
    box-sizing: border-box;
    margin: 10px 0;
    display: flex;
    flex-direction: column;
}

.customizationControl label {
    font-size: 14px;
    margin-bottom: 5px;
}

.customizationControl select,
.customizationControl input[type="color"] {
    box-sizing: border-box;
    margin: 0;
    padding: 5px;
    border-radius: 5px;
    border: none;
    background: #3a3a3a;
    color: #ffffff;
    font-size: 14px;
}

/* Annotation Edit Input */
.annotationEditInput {
    box-sizing: border-box;
    margin: 0;
    position: absolute;
    display: none;
    z-index: 2000;
    padding: 5px;
    font-size: 14px;
    border: 1px solid #ccc;
    background: #fff;
    color: #000;
    border-radius: 4px;
}

/* Media Queries */
@media (max-width: 1300px) {
    .modelViewerContainer {
        flex-direction: row;
        height: calc(100vh - 80px);
    }

    .modelViewer {
        height: 100%;
    }
}

@media (max-width: 600px) {
    .modelViewerContainer {
        flex-direction: column;
        height: calc(100vh - 80px);
    }

    .modelViewer {
        height: 300px;
    }

    .controls h3 {
        font-size: 16px;
    }

    .button {
        font-size: 14px;
        padding: 8px 16px;
    }
}