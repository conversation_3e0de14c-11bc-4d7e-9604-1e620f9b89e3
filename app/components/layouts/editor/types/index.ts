// File: components/layouts/editor/types/index.ts
import { ReactNode } from 'react';
import { SavedAnnotation } from '../ModelViewerCore/types';

// Type definitions for part categories and types
export type PartCategory =
  | 'Structural'
  | 'Electronics'
  | 'Motors'
  | '3D Printed Parts'
  | 'Fasteners';

export type PartType =
  | 'Stepper'
  | 'Servo'
  | 'DC'
  | 'PCB'
  | 'Sensor'
  | 'Microcontroller'
  | 'Screw'
  | 'Bolt'
  | 'Nut'
  | 'PLA'
  | 'ABS'
  | 'PETG'
  | 'Aluminum'
  | 'Steel'
  | 'Plastic';

// Interface for Part objects
export interface Part {
  id: string;
  name: string;
  description?: string;
  partNumber?: string;
  category: PartCategory;
  type?: PartType;
  visible?: boolean;
  locked?: boolean;
  rank?: number;
  specifications: Record<string, any>;
  assembly?: {
    notes?: string;
    maintenanceSchedule?: string;
    knownIssues?: string;
  };
  externalLinks?: {
    datasheetUrl?: string;
    supplierUrl?: string;
    referenceDoc?: string;
  };
  customFields?: {
    [key: string]: string;
  };
  children?: Part[];
  parentSwitchId?: string;
  relatedSwitchIds?: string[];
}

// Interface for Bill of Materials item
export interface BOMItem {
  id: string;
  name: string;
  partNumber: string;
  quantity: number;
}

// Panel configuration
export interface PanelConfig {
  id: string;
  title: string;
  component: React.ComponentType<any>;
  props?: any;
  defaultCollapsed?: boolean;
  enableSearch?: boolean;
  icon?: ReactNode;
}

// Tab configuration
export interface TabConfig {
  id: string;
  label: string;
  icon?: ReactNode;
  panels: PanelConfig[];
}

// Region configuration (left, center, right)
export interface RegionConfig {
  width?: number | string;
  tabs?: TabConfig[]; // Make tabs optional
  defaultTabId?: string;
  content?: React.ReactNode; // Add this for direct content
}

// Editor Layout Props
export interface EditorLayoutProps {
  leftRegion: RegionConfig;
  rightRegion: RegionConfig;
  centerContent?: ReactNode;
  onPartsChange?: (parts: Part[]) => void;
  onBOMChange?: (bomItems: BOMItem[]) => void;
  initialParts?: Part[];
  initialBOMItems?: BOMItem[];
}

// Panel Props
export interface EditorPanelProps {
  id: string;
  title: string;
  children: ReactNode;
  enableSearch?: boolean;
  defaultCollapsed?: boolean;
  onSearch?: (term: string) => void;
  className?: string;
  headerRightContent?: ReactNode;
  showTitle?: boolean;
}

// Tab Panel Props
export interface EditorTabPanelProps {
  tabs: TabConfig[];
  defaultTabId?: string;
  onChange?: (tabId: string) => void;
  className?: string;
}

// Context types for the Editor
export interface EditorContextType {
  parts: Part[];
  bomItems: BOMItem[];
  selectedPartId: string | null;
  setParts: (parts: Part[]) => void;
  setBOMItems: (bomItems: BOMItem[]) => void;
  setSelectedPartId: (id: string | null) => void;
  updatePart: (id: string, updates: Partial<Part>) => void;
  updateBOMItem: (id: string, updates: Partial<BOMItem>) => void;
  findPartById: (id: string) => Part | null;
}

export interface PointerStyle {
  color: string;
  borderStyle: string;
  borderWidth: string;
  size: string;
  opacity: number;
  font: string;
  alignment: string;
}

export interface PointerPosition {
  x: number;
  y: number;
  z?: number;
}

export interface Pointer {
  id: string;
  name: string;
  description?: string;
  style: PointerStyle;
  position?: PointerPosition;
  createdAt?: number;
  updatedAt?: number;
  isNew?: boolean;
  annotations?: SavedAnnotation[];
  interactiveMode?: InteractiveModePart[];
}

// Add 3D pointer related types to the existing types
export interface Pointer3DPosition {
  x: number;
  y: number;
  z: number;
}

// 3D model related types
export interface Model3D {
  id: string;
  url: string;
  name: string;
  description?: string;
  thumbnailUrl?: string;
  pointers?: Pointer[];
}

// Update annotation styles for 3D
export interface Annotation3DStyle extends PointerStyle {
  arrowType?: 'default' | 'arrow' | 'dot' | 'none';
  lineType?: 'solid' | 'dashed' | 'dotted' | 'none';
  scale?: number; // Scale factor for the annotation in 3D space
  followCamera?: boolean; // Whether the annotation should always face the camera
}

// Update the existing Pointer interface to include 3D specific properties
export interface Pointer3D extends Pointer {
  position3D?: Pointer3DPosition;
  targetObject?: string; // ID or name of the 3D object this pointer is attached to
  annotationType?: 'text' | 'arrow' | 'dimension' | 'custom';
}

// Interactive Mode Types
export interface InteractiveModePart {
  partId: string;
  isHighlighted: boolean;
  isIsolated: boolean;
  isHidden: boolean;
}

export interface InteractiveModeState {
  pointerId: string;
  parts: InteractiveModePart[];
  timestamp: number;
}

// Add these to the index.ts file, keeping all the existing types
