'use client';

import React from 'react';

interface PropertySectionProps {
  title: string;
  children: React.ReactNode;
  className?: string;
}

export const PropertySection: React.FC<PropertySectionProps> = ({
                                                                  title,
                                                                  children,
                                                                  className
                                                                }) => {
  return (
    <div className={className}>
      <h3 className='text-base font-medium text-white/70 mb-4'>{title}</h3>
      <div className='space-y-4'>{children}</div>
    </div>
  );
};

export default PropertySection;