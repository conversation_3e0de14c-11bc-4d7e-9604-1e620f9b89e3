import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { ChevronDown, ChevronRight } from 'lucide-react';
import { Part } from '../types';

interface PartTreeItemProps {
  part: Part;
  level: number;
  selectedId: string | null;
  onSelect: (id: string) => void;
  onDragStart: (e: React.DragEvent, id: string, level: number) => void;
  onDrop: (e: React.DragEvent, id: string) => void;
  onDragOver: (e: React.DragEvent) => void;
  onDragEnter: (e: React.DragEvent, id: string) => void;
  onDragLeave: (e: React.DragEvent, id: string) => void;
  isDragTarget: boolean;
}

export const PartTreeItem: React.FC<PartTreeItemProps> = ({
  part,
  level,
  selectedId,
  onSelect,
  onDragStart,
  onDrop,
  onDragOver,
  onDragEnter,
  onDragLeave,
  isDragTarget,
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const hasChildren = part.children && part.children.length > 0;

  // Get the appropriate icon for the part category

  return (
    <div
      className={cn(
        'w-full',
        isDragTarget && 'border-2 border-dashed border-[#AA423A]/50 rounded-md',
      )}
      onDrop={(e) => onDrop(e, part.id)}
      onDragOver={onDragOver}
      onDragEnter={(e) => onDragEnter(e, part.id)}
      onDragLeave={(e) => onDragLeave(e, part.id)}
    >
      <div
        className={cn(
          'flex items-center py-1.5 cursor-pointer',
          selectedId === part.id
            ? 'bg-[#AA423A]/20 text-white'
            : 'hover:bg-[#232326] text-white',
          selectedId === part.id ? 'border-[#AA423A]' : 'border-transparent',
        )}
        style={{ paddingLeft: `${level * 16 + 8}px` }}
        onClick={() => onSelect(part.id)}
        draggable
        onDragStart={(e) => onDragStart(e, part.id, level)}
      >
        <div className='flex items-center flex-1 px-4'>
          <span className='truncate flex-1'>{part.name}</span>

          {hasChildren && (
            <button
              className='p-1 hover:bg-[#313133] rounded-md mr-2'
              onClick={(e) => {
                e.stopPropagation();
                setIsExpanded(!isExpanded);
              }}
            >
              {isExpanded ? (
                <ChevronDown size={16} />
              ) : (
                <ChevronRight size={16} />
              )}
            </button>
          )}
        </div>
      </div>

      {isExpanded && hasChildren && (
        <div>
          {part.children?.map((child) => (
            <PartTreeItem
              key={child.id}
              part={child}
              level={level + 1}
              selectedId={selectedId}
              onSelect={onSelect}
              onDragStart={onDragStart}
              onDrop={onDrop}
              onDragOver={onDragOver}
              onDragEnter={onDragEnter}
              onDragLeave={onDragLeave}
              isDragTarget={isDragTarget}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default PartTreeItem;
