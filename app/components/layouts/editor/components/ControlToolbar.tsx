import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/app/components/ui/Button';
import { Icon } from '@iconify/react';
import { CaretDownIcon } from '@radix-ui/react-icons';
import { Check, Square, Circle } from 'lucide-react';
import { CustomRange } from '@/app/components/ui/CustomRange';

interface ToolState {
  subTool?: string;
}

interface ControlToolbarProps {
  onSelectTool?: (tool: string, subTool?: string) => void;
  onZoomChange?: (zoomLevel: number) => void;
  toolStates?: Record<string, ToolState>;
}

// Type for the active tool - stores both the main tool and subtool if any
interface ActiveToolState {
  mainTool: string;
  subTool?: string;
}

const ControlToolbar: React.FC<ControlToolbarProps> = ({
  onSelectTool,
  onZoomChange,
}) => {
  // Single state to track active tool
  const [activeTool, setActiveTool] = useState<ActiveToolState | null>(null);

  // States for submenus visibility
  const [showMoveSubmenu, setShowMoveSubmenu] = useState(false);
  const [showShapeSubmenu, setShowShapeSubmenu] = useState(false);
  const [showArrowSubmenu, setShowArrowSubmenu] = useState(false);
  const [show3DSlider, setShow3DSlider] = useState(false);

  // Zoom state for the 3D slider
  const [zoomLevel, setZoomLevel] = useState(50);

  // Track selected subtools for each main tool
  const [selectedSubTools, setSelectedSubTools] = useState({
    move: 'move-move',
    shape: 'shape-rectangle',
    arrow: 'arrow-straight',
    pen: 'shape-rectangle', // Add default for pen
    text: '', // Add default for text
    '3d': '', // Add default for 3d
  });

  // Debug: Log active tool changes
  useEffect(() => {}, [activeTool]);

  // Refs for detecting clicks outside
  const moveMenuRef = useRef<HTMLDivElement>(null);
  const shapeMenuRef = useRef<HTMLDivElement>(null);
  const arrowMenuRef = useRef<HTMLDivElement>(null);
  const sliderMenuRef = useRef<HTMLDivElement>(null);

  // Handle clicks outside the submenus and slider
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        moveMenuRef.current &&
        !moveMenuRef.current.contains(event.target as Node) &&
        !(event.target as Element).closest('[data-move-trigger="true"]')
      ) {
        setShowMoveSubmenu(false);
      }

      if (
        shapeMenuRef.current &&
        !shapeMenuRef.current.contains(event.target as Node) &&
        !(event.target as Element).closest('[data-shape-trigger="true"]')
      ) {
        setShowShapeSubmenu(false);
      }

      if (
        arrowMenuRef.current &&
        !arrowMenuRef.current.contains(event.target as Node) &&
        !(event.target as Element).closest('[data-arrow-trigger="true"]')
      ) {
        setShowArrowSubmenu(false);
      }

      if (
        sliderMenuRef.current &&
        !sliderMenuRef.current.contains(event.target as Node) &&
        !(event.target as Element).closest('[data-3d-trigger="true"]')
      ) {
        setShow3DSlider(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle click on main tool
  const handleToolClick = (tool: string) => {
    // First, update the active tool immediately to ensure UI updates
    if (tool === 'move') {
      setShowMoveSubmenu(!showMoveSubmenu);
      setShowShapeSubmenu(false);
      setShowArrowSubmenu(false);
      setShow3DSlider(false);

      // Set active tool with the current subtool for this tool
      const subTool = selectedSubTools.move || 'move-move';
      setActiveTool({ mainTool: tool, subTool });
      if (onSelectTool) onSelectTool(tool, subTool);
    } else if (tool === 'shape') {
      setShowShapeSubmenu(!showShapeSubmenu);
      setShowMoveSubmenu(false);
      setShowArrowSubmenu(false);
      setShow3DSlider(false);

      // Set active tool with the current subtool for this tool
      const subTool = selectedSubTools.shape || 'shape-rectangle';
      setActiveTool({ mainTool: tool, subTool });
      if (onSelectTool) onSelectTool(tool, subTool);
    } else if (tool === 'arrow') {
      setShowArrowSubmenu(!showArrowSubmenu);
      setShowMoveSubmenu(false);
      setShowShapeSubmenu(false);
      setShow3DSlider(false);

      // Set active tool with the current subtool for this tool
      const subTool = selectedSubTools.arrow || 'arrow-straight';
      setActiveTool({ mainTool: tool, subTool });
      if (onSelectTool) onSelectTool(tool, subTool);
    } else if (tool === '3d') {
      setShow3DSlider(!show3DSlider);
      setShowMoveSubmenu(false);
      setShowShapeSubmenu(false);
      setShowArrowSubmenu(false);

      setActiveTool({ mainTool: tool });
      if (onSelectTool) onSelectTool(tool);
    } else if (tool === 'pen') {
      // For pen tool, we want to use the saved shape from shape tool
      setShowMoveSubmenu(false);
      setShowShapeSubmenu(false);
      setShowArrowSubmenu(false);
      setShow3DSlider(false);

      // Get the current shape subtool
      const shapeSubTool = selectedSubTools.shape || 'shape-rectangle';

      // Important: Set activeTool directly
      setActiveTool({ mainTool: 'pen', subTool: shapeSubTool });

      // Update the selected subtool for pen
      setSelectedSubTools((prev) => ({
        ...prev,
        pen: shapeSubTool,
      }));

      if (onSelectTool) {
        onSelectTool('pen', shapeSubTool);
      }
    } else if (tool === 'text') {
      // For text tool
      setShowMoveSubmenu(false);
      setShowShapeSubmenu(false);
      setShowArrowSubmenu(false);
      setShow3DSlider(false);

      // Update active tool to text
      setActiveTool({ mainTool: 'text' });

      if (onSelectTool) {
        onSelectTool('text');
      }
    } else {
      // For other direct tools
      setShowMoveSubmenu(false);
      setShowShapeSubmenu(false);
      setShowArrowSubmenu(false);
      setShow3DSlider(false);

      // Update active tool
      setActiveTool({ mainTool: tool });
      if (onSelectTool) onSelectTool(tool);
    }
  };

  // Handle selection of subtool
  const handleSubToolSelect = (mainTool: string, subTool: string) => {
    // Update the selected subtool for this main tool
    setSelectedSubTools((prev) => ({
      ...prev,
      [mainTool]: subTool,
    }));

    // Update active tool
    setActiveTool({ mainTool, subTool });

    if (onSelectTool) {
      onSelectTool(mainTool, subTool);
    }
  };

  // Handle zoom slider change
  const handleZoomChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newZoomLevel = parseInt(e.target.value);
    setZoomLevel(newZoomLevel);

    if (onZoomChange) {
      onZoomChange(newZoomLevel);
    }
  };

  // Check if a tool is active - with extra debugging
  const isToolActive = (tool: string): boolean => {
    const isActive = activeTool?.mainTool === tool;
    // console.log(`Checking if ${tool} is active:`, isActive, "activeTool:", activeTool);
    return isActive;
  };

  // Check if a subtool is active
  const isSubToolActive = (subTool: string) => {
    // Check if it's the currently active subtool
    if (activeTool?.subTool === subTool) {
      return true;
    }

    // Check if it's the selected subtool for its main tool category
    if (subTool.startsWith('move-') && selectedSubTools.move === subTool) {
      return true;
    }
    if (subTool.startsWith('shape-') && selectedSubTools.shape === subTool) {
      return true;
    }
    if (subTool.startsWith('arrow-') && selectedSubTools.arrow === subTool) {
      return true;
    }

    return false;
  };

  // Consistent submenu styling
  const submenuStyle =
    'absolute bottom-14 bg-[#232326] px-2 py-2 rounded-lg shadow-lg z-10 min-w-[9.5rem]';
  const submenuItemStyle =
    'flex items-center gap-2 w-full px-2 py-1.5 rounded text-white/70 hover:bg-[#AA423A]/50 hover:text-white cursor-pointer';
  const activeItemStyle = 'bg-[#AA423A]/20';

  // Helper to render the correct icon for the main toolbar buttons
  const getToolIcon = (baseTool: string) => {
    if (baseTool === 'move') {
      // Show the current move subtool icon
      const currentSubTool = selectedSubTools.move;
      if (currentSubTool === 'move-pan') {
        return (
          <Icon
            icon='ph:hand-thin'
            style={{ width: '1.25rem', height: '1.25rem' }}
          />
        );
      } else if (currentSubTool === 'move-zoom') {
        return (
          <Icon
            icon='iconamoon:zoom-in-thin'
            style={{ width: '1.25rem', height: '1.25rem' }}
          />
        );
      }
      // Default move icon
      return (
        <Icon
          icon='lsicon:pointer-outline'
          style={{ width: '1.25rem', height: '1.25rem' }}
        />
      );
    } else if (baseTool === 'shape') {
      // Show the current shape subtool icon
      const currentSubTool = selectedSubTools.shape;
      if (currentSubTool === 'shape-rectangle') {
        return <Square style={{ width: '1.25rem', height: '1.25rem' }} />;
      } else if (currentSubTool === 'shape-ellipse') {
        return <Circle style={{ width: '1.25rem', height: '1.25rem' }} />;
      } else if (currentSubTool === 'shape-cylinder') {
        return (
          <Icon
            icon='ph:cylinder-thin'
            style={{ width: '1.25rem', height: '1.25rem' }}
          />
        );
      }
      // Default shape icon
      return <Square style={{ width: '1.25rem', height: '1.25rem' }} />;
    } else if (baseTool === 'arrow') {
      // Show the current arrow subtool icon
      const currentSubTool = selectedSubTools.arrow;
      if (currentSubTool === 'arrow-straight') {
        return (
          <Icon
            icon='stash:arrow-up-light'
            style={{ width: '1.25rem', height: '1.25rem' }}
          />
        );
      } else if (currentSubTool === 'arrow-curve') {
        return (
          <Icon
            icon='fluent:arrow-curve-up-right-20-regular'
            style={{ width: '1.25rem', height: '1.25rem' }}
          />
        );
      }
      // Default arrow icon
      return (
        <Icon
          icon='stash:arrow-up-light'
          style={{ width: '1.25rem', height: '1.25rem' }}
        />
      );
    } else if (baseTool === 'pen') {
      // Always use the pen icon for the pen tool, not the shape icon
      return (
        <Icon
          icon='hugeicons:pen-tool-03'
          style={{ width: '1.25rem', height: '1.25rem' }}
        />
      );
    } else if (baseTool === '3d') {
      return (
        <Icon
          icon='tabler:cube-3d-sphere'
          style={{ width: '1.25rem', height: '1.25rem' }}
        />
      );
    } else if (baseTool === 'text') {
      return (
        <Icon
          icon='iconoir:text'
          style={{ width: '1.25rem', height: '1.25rem' }}
        />
      );
    }

    // Default fallback
    return (
      <Icon
        icon='lsicon:pointer-outline'
        style={{ width: '1.25rem', height: '1.25rem' }}
      />
    );
  };

  return (
    <div className='relative'>
      {/* Move submenu */}
      {showMoveSubmenu && (
        <div ref={moveMenuRef} className={`${submenuStyle} left-0`}>
          <div className='flex flex-col w-full items-start space-y-1'>
            <button
              className={`${submenuItemStyle} ${isSubToolActive('move-move') ? activeItemStyle : ''}`}
              onClick={() => handleSubToolSelect('move', 'move-move')}
            >
              {isSubToolActive('move-move') && (
                <Check className='w-4 h-4 mr-1' />
              )}
              <Icon
                icon='lsicon:pointer-outline'
                style={{ width: '1rem', height: '1rem' }}
              />
              <span>Move</span>
            </button>
            <button
              className={`${submenuItemStyle} ${isSubToolActive('move-pan') ? activeItemStyle : ''}`}
              onClick={() => handleSubToolSelect('move', 'move-pan')}
            >
              {isSubToolActive('move-pan') && (
                <Check className='w-4 h-4 mr-1' />
              )}
              <Icon
                icon='ph:hand-thin'
                style={{ width: '1rem', height: '1rem' }}
              />
              <span>Pan</span>
            </button>
            <button
              className={`${submenuItemStyle} ${isSubToolActive('move-zoom') ? activeItemStyle : ''}`}
              onClick={() => handleSubToolSelect('move', 'move-zoom')}
            >
              {isSubToolActive('move-zoom') && (
                <Check className='w-4 h-4 mr-1' />
              )}
              <Icon
                icon='iconamoon:zoom-in-thin'
                style={{ width: '1rem', height: '1rem' }}
              />
              <span>Zoom</span>
            </button>
          </div>
        </div>
      )}

      {/* Shape submenu */}
      {showShapeSubmenu && (
        <div ref={shapeMenuRef} className={`${submenuStyle} left-12`}>
          <div className='flex flex-col space-y-1'>
            <button
              className={`${submenuItemStyle} ${
                isSubToolActive('shape-rectangle') ||
                (activeTool?.mainTool === 'pen' &&
                  activeTool?.subTool === 'shape-rectangle')
                  ? activeItemStyle
                  : ''
              }`}
              onClick={() => handleSubToolSelect('shape', 'shape-rectangle')}
            >
              {isSubToolActive('shape-rectangle') && (
                <Check className='w-4 h-4 mr-1' />
              )}
              <Square style={{ width: '1rem', height: '1rem' }} />
              <span>Rectangle</span>
            </button>
            <button
              className={`${submenuItemStyle} ${
                isSubToolActive('shape-ellipse') ||
                (activeTool?.mainTool === 'pen' &&
                  activeTool?.subTool === 'shape-ellipse')
                  ? activeItemStyle
                  : ''
              }`}
              onClick={() => handleSubToolSelect('shape', 'shape-ellipse')}
            >
              {isSubToolActive('shape-ellipse') && (
                <Check className='w-4 h-4 mr-1' />
              )}
              <Circle style={{ width: '1rem', height: '1rem' }} />
              <span>Ellipse</span>
            </button>
            <button
              className={`${submenuItemStyle} ${
                isSubToolActive('shape-cylinder') ||
                (activeTool?.mainTool === 'pen' &&
                  activeTool?.subTool === 'shape-cylinder')
                  ? activeItemStyle
                  : ''
              }`}
              onClick={() => handleSubToolSelect('shape', 'shape-cylinder')}
            >
              {isSubToolActive('shape-cylinder') && (
                <Check className='w-4 h-4 mr-1' />
              )}
              <Icon
                icon='ph:cylinder-thin'
                style={{ width: '1rem', height: '1rem' }}
              />
              <span>Cylinder</span>
            </button>
          </div>
        </div>
      )}

      {/* Arrow submenu */}
      {showArrowSubmenu && (
        <div ref={arrowMenuRef} className={`${submenuStyle} left-24`}>
          <div className='flex flex-col space-y-1'>
            <button
              className={`${submenuItemStyle} ${
                isSubToolActive('arrow-straight') ||
                (activeTool?.mainTool === 'pen' &&
                  activeTool?.subTool === 'shape-arrow')
                  ? activeItemStyle
                  : ''
              }`}
              onClick={() => handleSubToolSelect('arrow', 'arrow-straight')}
            >
              {isSubToolActive('arrow-straight') && (
                <Check className='w-4 h-4 mr-1' />
              )}
              <Icon
                icon='stash:arrow-up-light'
                style={{ width: '1rem', height: '1rem' }}
              />
              <span>Straight Arrow</span>
            </button>
            <button
              className={`${submenuItemStyle} ${
                isSubToolActive('arrow-curve') ||
                (activeTool?.mainTool === 'pen' &&
                  activeTool?.subTool === 'shape-curve')
                  ? activeItemStyle
                  : ''
              }`}
              onClick={() => handleSubToolSelect('arrow', 'arrow-curve')}
            >
              {isSubToolActive('arrow-curve') && (
                <Check className='w-4 h-4 mr-1' />
              )}
              <Icon
                icon='fluent:arrow-curve-up-right-20-regular'
                style={{ width: '1rem', height: '1rem' }}
              />
              <span>Curved Arrow</span>
            </button>
          </div>
        </div>
      )}

      {/* 3D Zoom Slider */}
      {show3DSlider && (
        <div
          ref={sliderMenuRef}
          className={`${submenuStyle} left-40 flex items-center w-48 p-3`}
        >
          <CustomRange
            min={0}
            max={100}
            value={zoomLevel}
            onChange={handleZoomChange}
            className='mx-2 flex-1'
            fillColor='#AA423A'
            trackColor='#242425'
          />
        </div>
      )}

      {/* Main toolbar */}
      <div className='flex items-center gap-4 w-full min-w-[17rem] bg-[#242425] border border-[#313133] rounded-xl px-3 py-2'>
        {/* Move tool */}
        <div
          className='flex items-center cursor-pointer'
          onClick={() => handleToolClick('move')}
          data-move-trigger='true'
        >
          <Button
            size='icon'
            variant='ghost'
            className={`w-8 h-8 flex items-center justify-center rounded ${
              isToolActive('move') ? 'bg-[#AA423A]' : ''
            }`}
            aria-label='Move tool'
          >
            {getToolIcon('move')}
          </Button>
          <CaretDownIcon className='h-2.5 w-2.5' />
        </div>

        {/* Shape tool */}
        <div
          className='flex items-center cursor-pointer'
          onClick={() => handleToolClick('shape')}
          data-shape-trigger='true'
        >
          <Button
            size='icon'
            variant='ghost'
            className={`w-8 h-8 flex items-center justify-center rounded ${
              isToolActive('shape') ? 'bg-[#AA423A]' : ''
            }`}
            aria-label='Shape tool'
          >
            {getToolIcon('shape')}
          </Button>
          <CaretDownIcon className='h-2.5 w-2.5' />
        </div>

        {/* Arrow tool (new) */}
        <div
          className='flex items-center cursor-pointer'
          onClick={() => handleToolClick('arrow')}
          data-arrow-trigger='true'
        >
          <Button
            size='icon'
            variant='ghost'
            className={`w-8 h-8 flex items-center justify-center rounded ${
              isToolActive('arrow') ? 'bg-[#AA423A]' : ''
            }`}
            aria-label='Arrow tool'
          >
            {getToolIcon('arrow')}
          </Button>
          <CaretDownIcon className='h-2.5 w-2.5' />
        </div>

        {/* Pen tool - Force bg-[#AA423A] when pen is active */}
        <Button
          size='icon'
          variant='ghost'
          className={`w-8 h-8 flex items-center justify-center rounded ${
            isToolActive('pen') ? 'bg-[#AA423A]' : ''
          }`}
          onClick={() => handleToolClick('pen')}
          aria-label='Pen tool'
          data-active={activeTool?.mainTool === 'pen'}
        >
          {getToolIcon('pen')}
        </Button>

        {/* Text tool - Force bg-[#AA423A] when text is active */}
        <Button
          size='icon'
          variant='ghost'
          className={`w-8 h-8 flex items-center justify-center rounded ${
            isToolActive('text') ? 'bg-[#AA423A]' : ''
          }`}
          onClick={() => handleToolClick('text')}
          aria-label='Text tool'
          data-active={activeTool?.mainTool === 'text'}
        >
          {getToolIcon('text')}
        </Button>

        {/* 3D tool */}
        <div
          className='flex items-center cursor-pointer'
          onClick={() => handleToolClick('3d')}
          data-3d-trigger='true'
        >
          <Button
            size='icon'
            variant='ghost'
            className={`w-8 h-8 flex items-center justify-center rounded ${
              isToolActive('3d') ? 'bg-[#AA423A]' : ''
            }`}
            aria-label='3D tool'
          >
            {getToolIcon('3d')}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ControlToolbar;
