import React from 'react';
import { Input } from '@/app/components/ui/Input';
import { NumericInput } from '@/app/components/inputs/NumericInput';
import SelectAccordion from '@/app/components/ui/SelectAccordion';

// Dynamic form field based on field type
interface DynamicFieldProps {
  label: string;
  type: string;
  value: any;
  onChange: (value: any) => void;
  options?: { value: string; label: string }[];
  min?: number;
  max?: number;
  step?: number;
  placeholder?: string;
  className?: string;
}

export const DynamicField: React.FC<DynamicFieldProps> = ({
  label,
  type,
  value,
  onChange,
  options = [],
  min,
  max,
  step,
  placeholder,
  className,
}) => {
  switch (type) {
    case 'Numeric':
      return (
        <NumericInput
          label={label}
          value={value || 0}
          onChange={onChange}
          min={min}
          max={max}
          step={step}
          className={className}
        />
      );
    case 'Text':
      return (
        <div className={`flex flex-col space-y-2 ${className}`}>
          <label className='text-sm text-white/70'>{label}</label>
          <Input
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={placeholder || `Enter ${label}`}
          />
        </div>
      );
    case 'TextArea':
      return (
        <div className={`flex flex-col space-y-2 ${className}`}>
          <label className='text-sm text-white/70'>{label}</label>
          <textarea
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={placeholder || `Enter ${label}`}
            className='w-full h-24 px-3 py-2 rounded-lg border border-[#313133] text-white bg-transparent resize-none'
          />
        </div>
      );
    case 'Dropdown':
      return (
        <div className={`flex flex-col space-y-2 ${className}`}>
          <label className='text-sm text-white/70'>{label}</label>
          <SelectAccordion
            options={options}
            defaultValue={value || ''}
            onChange={onChange}
            placeholder={`Select ${label}`}
          />
        </div>
      );
    case 'Dimensions':
      const dimensionValues = value
        ? value.split('x').map((v: string) => v.trim())
        : ['', '', ''];
      return (
        <div className={`flex flex-col space-y-2 ${className}`}>
          <label className='text-sm text-white/70'>{label}</label>
          <div className='flex items-center space-x-2'>
            <Input
              value={dimensionValues[0] || ''}
              onChange={(e) => {
                const newValues = [...dimensionValues];
                newValues[0] = e.target.value;
                onChange(newValues.join(' x '));
              }}
              placeholder='Length'
            />
            <span className='text-white/70'>×</span>
            <Input
              value={dimensionValues[1] || ''}
              onChange={(e) => {
                const newValues = [...dimensionValues];
                newValues[1] = e.target.value;
                onChange(newValues.join(' x '));
              }}
              placeholder='Width'
            />
            <span className='text-white/70'>×</span>
            <Input
              value={dimensionValues[2] || ''}
              onChange={(e) => {
                const newValues = [...dimensionValues];
                newValues[2] = e.target.value;
                onChange(newValues.join(' x '));
              }}
              placeholder='Height'
            />
          </div>
        </div>
      );
    case 'Range':
      const rangeValues = value
        ? value.split('to').map((v: string) => v.trim())
        : ['', ''];
      return (
        <div className={` flex flex-col space-y-2 ${className}`}>
          <label className='text-sm text-white/70'>{label}</label>
          <div className='flex items-center space-x-2'>
            <Input
              value={rangeValues[0] || ''}
              onChange={(e) => {
                const newValues = [...rangeValues];
                newValues[0] = e.target.value;
                onChange(newValues.join(' to '));
              }}
              placeholder='Min'
            />
            <span className='text-white/70'>to</span>
            <Input
              value={rangeValues[1] || ''}
              onChange={(e) => {
                const newValues = [...rangeValues];
                newValues[1] = e.target.value;
                onChange(newValues.join(' to '));
              }}
              placeholder='Max'
            />
          </div>
        </div>
      );
    default:
      return (
        <div className={`space-y-2 ${className}`}>
          <label className='text-sm text-white/70'>{label}</label>
          <Input
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={placeholder || `Enter ${label}`}
          />
        </div>
      );
  }
};

export default DynamicField;
