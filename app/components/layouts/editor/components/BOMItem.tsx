import React from 'react';
import { NumericInput } from '@/app/components/inputs/NumericInput'; // Assuming this component exists
import { BOMItem } from '../types';

interface BOMItemComponentProps {
  item: BOMItem;
  onQuantityChange: (id: string, quantity: number) => void;
}

export const BOMItemComponent: React.FC<BOMItemComponentProps> = ({
  item,
  onQuantityChange,
}) => {
  return (
    <div className='flex items-center justify-between py-1.5'>
      <div className='truncate flex-1'>{item.name}</div>
      <div className='text-white text-sm mr-10'>{item.partNumber}</div>
      <div className='flex items-center'>
        <NumericInput
          value={item.quantity}
          onChange={(value) => onQuantityChange(item.id, value)}
          min={0}
          max={999}
          step={1}
          className='w-24'
        />
      </div>
    </div>
  );
};

export default BOMItemComponent;
