import React from 'react';
import { Button } from '@/app/components/ui/Button';
import { Icon } from '@iconify/react';

interface HistoryControlsProps {
  canUndo: boolean;
  canRedo: boolean;
  onUndo: () => void;
  onRedo: () => void;
}

const HistoryControls: React.FC<HistoryControlsProps> = ({
  canUndo,
  canRedo,
  onUndo,
  onRedo,
}) => {
  return (
    <div className='flex items-center gap-1 p-3 bg-[#242425] border-[#313133] rounded-xl'>
      <Button
        size='icon'
        variant='ghost'
        className={`w-8 h-8 flex items-center justify-center rounded ${
          !canUndo ? 'opacity-50 cursor-not-allowed' : ''
        }`}
        onClick={onUndo}
        disabled={!canUndo}
        aria-label='Undo'
        title='Undo'
      >
        <Icon icon='solar:undo-left-linear' className='w-5 h-5' />
      </Button>
      <Button
        size='icon'
        variant='ghost'
        className={`w-8 h-8 flex items-center justify-center rounded ${
          !canRedo ? 'opacity-50 cursor-not-allowed' : ''
        }`}
        onClick={onRedo}
        disabled={!canRedo}
        aria-label='Redo'
        title='Redo'
      >
        <Icon icon='solar:undo-right-linear' className='w-5 h-5' />
      </Button>
    </div>
  );
};

export default HistoryControls;
