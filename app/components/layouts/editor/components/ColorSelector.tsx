import React from 'react';
import { CustomRange } from '@/app/components/ui/CustomRange';
import { PointerStyle } from '../types/index';

// Import SVG icons

// Define types for the line texture options
type LineStyle = 'solid' | 'dashed' | 'dotted' | 'none';
type LineSize = 'S' | 'M' | 'L' | 'XL';

interface ColorSelectorProps {
  style: PointerStyle;
  onStyleChange: (updates: Partial<PointerStyle>) => void;
}

const ColorSelector: React.FC<ColorSelectorProps> = ({
  style,
  onStyleChange,
}) => {
  // Color options matching the UI in the image
  const colors = [
    '#000000', // Black
    '#8D8D8D', // Gray
    '#E56DEE', // Light Purple
    '#9C57E0', // Purple
    '#3B82F6', // Blue
    '#63B3FB', // Light Blue
    '#F3B53B', // Gold
    '#E17D38', // Orange
    '#2D9D78', // Green
    '#66D066', // Light Green
    '#F87979', // Pink
    '#E64343', // Red
  ];

  // Line style options
  const lineStyles: { id: LineStyle; label: string }[] = [
    { id: 'solid', label: 'Solid Line' },
    { id: 'dashed', label: 'Dashed Line' },
    { id: 'dotted', label: 'Dotted Line' },
    { id: 'none', label: 'No Line' },
  ];

  // Line sizes
  const sizes: LineSize[] = ['S', 'M', 'L', 'XL'];

  const handleRangeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onStyleChange({ opacity: parseInt(e.target.value) });
  };

  return (
    <div className='bg-[#242425] rounded-xl shadow-lg'>
      {/* Color Grid */}
      <div className='grid grid-cols-4 gap-3 px-3 py-2 mb-4'>
        {colors.map((color, index) => (
          <button
            key={index}
            className={`w-6 h-6 rounded-full cursor-pointer ${
              style.color === color ? 'ring-2 ring-[#AA423A]/20' : ''
            }`}
            style={{ backgroundColor: color }}
            onClick={() => onStyleChange({ color })}
            aria-label={`Select color ${index + 1}`}
            aria-pressed={style.color === color}
          />
        ))}
      </div>

      {/* Range slider */}
      <div className='mb-4 px-3'>
        <CustomRange
          min={1}
          max={100}
          value={style.opacity}
          trackHeight='3px'
          onChange={handleRangeChange}
          fillColor='#024A43'
          trackColor='#313133'
        />
      </div>

      {/* Divider */}
      <div className='border-b border-gray-200 mb-4'></div>

      {/* Line Style Layer */}
      <div className='flex justify-between mb-4 px-3'>
        {lineStyles.map((lineStyle) => (
          <button
            key={lineStyle.id}
            type='button'
            onClick={() => onStyleChange({ borderStyle: lineStyle.id })}
            className={`w-10 h-10 p-2 rounded-md flex items-center justify-center cursor-pointer ${
              style.borderStyle === lineStyle.id ? 'bg-[#313133]' : ''
            }`}
            aria-label={lineStyle.label}
          >
            {lineStyle.id === 'solid' && (
              <div
                className='w-6 h-6 rounded-full border-3  border-solid'
                style={{ borderColor: style.color ?? '#ffffff' }}
              ></div>
            )}
            {lineStyle.id === 'dashed' && (
              <div
                className='w-6 h-6 border-3 border-dashed rounded-full'
                style={{ borderColor: style.color ?? '#ffffff' }}
              ></div>
            )}
            {lineStyle.id === 'dotted' && (
              <div
                className='w-6 h-6 border-3 border-dotted rounded-full'
                style={{ borderColor: style.color ?? '#ffffff' }}
              ></div>
            )}
            {lineStyle.id === 'none' && (
              <div
                className='w-6 h-6 border border-current rounded-full'
                style={{ borderColor: style.color ?? '#ffffff' }}
              ></div>
            )}
          </button>
        ))}
      </div>

      {/* Size Options */}
      <div className='flex justify-between px-3 pb-3'>
        {sizes.map((size) => (
          <button
            key={size}
            className={`w-8 h-8 flex items-center cursor-pointer justify-center text-lg font-bold rounded-md
              ${
                style.size === size
                  ? 'bg-[#313133] text-white'
                  : 'hover:text-white hover:bg-[#313133]'
              }`}
            onClick={() => onStyleChange({ size })}
            aria-label={`Select size ${size}`}
            aria-pressed={style.size === size}
          >
            {size}
          </button>
        ))}
      </div>
    </div>
  );
};

export default ColorSelector;
