import React, { useState, useEffect } from 'react';
import { Icon } from '@iconify/react';
import { Square, Circle } from 'lucide-react';

interface CustomCursorProps {
  selectedTool: {
    mainTool: string;
    subTool?: string;
  } | null;
  isDrawing?: boolean;
  drawingColor?: string;
  drawingStyle?: string;
}

const CustomCursor: React.FC<CustomCursorProps> = ({
  selectedTool,
  isDrawing = false,
  drawingColor = '#ffffff',
  drawingStyle = 'solid',
}) => {
  const [position, setPosition] = useState({ x: -100, y: -100 }); // Start offscreen
  const [isVisible, setIsVisible] = useState(false);

  // Update cursor position on mouse move
  useEffect(() => {
    const updatePosition = (e: MouseEvent) => {
      // Only show cursor when mouse is over the canvas
      if (e.target && (e.target as Element).closest('.canvas-area')) {
        setIsVisible(true);
        setPosition({ x: e.clientX, y: e.clientY });
      } else {
        setIsVisible(false);
      }
    };

    window.addEventListener('mousemove', updatePosition);
    return () => window.removeEventListener('mousemove', updatePosition);
  }, []);

  // No need to show custom cursor if no tool is selected
  if (!selectedTool || !isVisible) return null;

  // Get the appropriate icon based on the selected tool
  const getCursorIcon = () => {
    const iconStyle = {
      color: drawingColor,
      filter: 'drop-shadow(0px 0px 2px rgba(0, 0, 0, 0.8))',
      width: '20px',
      height: '20px',
    };

    // If we're in drawing mode with pen tool, show a drawing cursor
    if (selectedTool.mainTool === 'pen' && isDrawing) {
      return (
        <div className='relative'>
          <div
            className='absolute w-2 h-2 rounded-full'
            style={{ backgroundColor: drawingColor, top: '-1px', left: '-1px' }}
          />
        </div>
      );
    }

    switch (selectedTool.mainTool) {
      case 'move':
        if (selectedTool.subTool === 'move-pan')
          return <Icon icon='ph:hand-grabbing-thin' style={iconStyle} />;
        if (selectedTool.subTool === 'move-zoom')
          return <Icon icon='iconamoon:zoom-in-thin' style={iconStyle} />;
        return <Icon icon='lsicon:pointer-outline' style={iconStyle} />;

      case 'shape':
        if (selectedTool.subTool === 'shape-rectangle')
          return <Square style={iconStyle} />;
        if (selectedTool.subTool === 'shape-arrow')
          return <Icon icon='stash:arrow-up-light' style={iconStyle} />;
        if (selectedTool.subTool === 'shape-ellipse')
          return <Circle style={iconStyle} />;
        if (selectedTool.subTool === 'shape-cylinder')
          return <Icon icon='ph:cylinder-thin' style={iconStyle} />;
        if (selectedTool.subTool === 'shape-curve')
          return (
            <Icon
              icon='fluent:arrow-curve-up-right-20-regular'
              style={iconStyle}
            />
          );
        return <Square style={iconStyle} />;

      case 'pen':
        return <Icon icon='hugeicons:pen-tool-03' style={iconStyle} />;

      case '3d':
        return <Icon icon='tabler:cube-3d-sphere' style={iconStyle} />;

      case 'text':
        return <Icon icon='iconoir:text' style={iconStyle} />;

      default:
        return null;
    }
  };

  // Calculate positioning offsets based on the tool
  // to ensure the "active point" of the cursor is positioned correctly
  const getOffset = () => {
    if (selectedTool.mainTool === 'shape') return { x: -10, y: -10 }; // Center for drawing tools
    if (selectedTool.subTool === 'move-pan') return { x: -10, y: -10 }; // Center for pan
    if (selectedTool.mainTool === 'pen') return { x: 0, y: 0 }; // No offset for pen tip

    return { x: 0, y: 0 }; // Default offset
  };

  const offset = getOffset();

  // This ensures the system cursor is hidden where our custom cursor appears
  const cursorStyle = {
    position: 'fixed' as const,
    top: position.y + offset.y,
    left: position.x + offset.x,
    zIndex: 9999,
    pointerEvents: 'none' as const,
    transform: 'translateZ(0)', // Hardware acceleration
  };

  // Render drawing path indicator for pen tool
  const renderDrawingIndicator = () => {
    if (selectedTool.mainTool === 'pen') {
      // Drawing style indicator
      let strokeStyle = '2px solid';
      if (drawingStyle === 'dashed') strokeStyle = '2px dashed';
      if (drawingStyle === 'dotted') strokeStyle = '2px dotted';

      return (
        <div
          className='absolute'
          style={{
            width: '10px',
            height: '10px',
            border: strokeStyle,
            borderColor: drawingColor,
            borderRadius: drawingStyle === 'dotted' ? '50%' : '0',
            bottom: '-15px',
            left: '5px',
          }}
        />
      );
    }
    return null;
  };

  // The crosshair that appears for shape tools
  const renderCrosshair = () => {
    if (selectedTool.mainTool === 'shape') {
      return (
        <>
          <div
            style={{
              position: 'absolute',
              height: '1px',
              width: '20px',
              backgroundColor: 'white',
              left: '-10px',
              top: '0',
              boxShadow: '0 0 2px rgba(0, 0, 0, 0.8)',
            }}
          />
          <div
            style={{
              position: 'absolute',
              width: '1px',
              height: '20px',
              backgroundColor: 'white',
              top: '-10px',
              left: '0',
              boxShadow: '0 0 2px rgba(0, 0, 0, 0.8)',
            }}
          />
        </>
      );
    }
    return null;
  };

  return (
    <div style={cursorStyle}>
      {getCursorIcon()}
      {renderCrosshair()}
      {renderDrawingIndicator()}
    </div>
  );
};

export default CustomCursor;
