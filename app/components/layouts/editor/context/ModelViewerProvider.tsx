'use client';

import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  useEffect,
} from 'react';
import {
  SavedAnnotation,
  ToolSelection,
} from '@/app/components/layouts/editor/ModelViewerCore';
import { PointerStyle } from '@/app/components/layouts/editor';
import { useEditorCoordinator } from './EditorCoordinator';

interface ModelViewerContextType {
  // Tool state
  selectedTool: ToolSelection | null;
  setSelectedTool: (tool: ToolSelection | null) => void;

  // Current pointer state (derived from coordinator)
  currentPointerId: string | null;
  currentPointerStyle: PointerStyle | null;

  // Annotation management
  saveAnnotationsForCurrentPointer: (annotations: SavedAnnotation[]) => void;
  getAnnotationsForCurrentPointer: () => SavedAnnotation[];
}

const ModelViewerContext = createContext<ModelViewerContextType>(
  {} as ModelViewerContextType,
);

const POINTERS_STORAGE_KEY = 'tnkr_pointers';

export const ModelViewerProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const { currentPointerId } = useEditorCoordinator();
  const [selectedTool, setSelectedTool] = useState<ToolSelection | null>(null);
  const [currentPointerStyle, setCurrentPointerStyle] =
    useState<PointerStyle | null>(null);

  // Track current pointer ID changes

  // Update pointer style when currentPointerId changes
  useEffect(() => {
    console.log(
      'ModelViewerProvider: currentPointerId changed to:',
      currentPointerId,
    );

    if (currentPointerId) {
      // Load pointer style from localStorage
      try {
        const stored = localStorage.getItem(POINTERS_STORAGE_KEY);
        console.log('found stored items', stored);
        if (stored) {
          const pointers = JSON.parse(stored);
          const currentPointer = pointers.find(
            (p: any) => p.id === currentPointerId,
          );
          console.log(
            'ModelViewerProvider: Found pointer in storage:',
            currentPointer,
          );

          if (currentPointer && currentPointer.style) {
            console.log(
              'ModelViewerProvider: Setting pointer style:',
              currentPointer.style,
            );
            setCurrentPointerStyle(currentPointer.style);
          } else {
            console.log(
              'ModelViewerProvider: No style found for pointer, setting to null',
            );
            setCurrentPointerStyle(null);
          }
        } else {
          console.log('ModelViewerProvider: No pointers found in storage');
          setCurrentPointerStyle(null);
        }
      } catch (error) {
        console.error(
          'ModelViewerProvider: Error loading current pointer style:',
          error,
        );
        setCurrentPointerStyle(null);
      }
    } else {
      console.log(
        'ModelViewerProvider: No currentPointerId, setting style to null',
      );
      setCurrentPointerStyle(null);
    }
  }, [currentPointerId]);

  const saveAnnotationsForCurrentPointer = useCallback(
    (annotations: SavedAnnotation[]) => {
      if (!currentPointerId) {
        console.warn(
          'ModelViewerProvider: No current pointer ID to save annotations to',
        );
        return;
      }

      console.log(
        `ModelViewerProvider: Saving ${annotations.length} annotations for pointer:`,
        currentPointerId,
      );

      try {
        // Update the pointer in localStorage with new annotations
        const stored = localStorage.getItem(POINTERS_STORAGE_KEY);
        if (stored) {
          const pointers = JSON.parse(stored);
          const updatedPointers = pointers.map((p: any) =>
            p.id === currentPointerId
              ? { ...p, annotations, updatedAt: Date.now() }
              : p,
          );
          localStorage.setItem(
            POINTERS_STORAGE_KEY,
            JSON.stringify(updatedPointers),
          );
          console.log(
            'ModelViewerProvider: Successfully saved annotations to localStorage',
          );
        }
      } catch (error) {
        console.error('ModelViewerProvider: Error saving annotations:', error);
      }
    },
    [currentPointerId],
  );

  const getAnnotationsForCurrentPointer = useCallback((): SavedAnnotation[] => {
    if (!currentPointerId) {
      console.log(
        'ModelViewerProvider: No current pointer ID, returning empty annotations',
      );
      return [];
    }

    try {
      const stored = localStorage.getItem(POINTERS_STORAGE_KEY);
      if (stored) {
        const pointers = JSON.parse(stored);
        const currentPointer = pointers.find(
          (p: any) => p.id === currentPointerId,
        );
        if (currentPointer && currentPointer.annotations) {
          console.log(
            `ModelViewerProvider: Loading ${currentPointer.annotations.length} annotations for pointer:`,
            currentPointerId,
          );
          return currentPointer.annotations;
        }
      }
    } catch (error) {
      console.error('ModelViewerProvider: Error loading annotations:', error);
    }

    console.log(
      'ModelViewerProvider: No annotations found for pointer:',
      currentPointerId,
    );
    return [];
  }, [currentPointerId]);

  return (
    <ModelViewerContext.Provider
      value={{
        selectedTool,
        setSelectedTool,
        currentPointerId,
        currentPointerStyle,
        saveAnnotationsForCurrentPointer,
        getAnnotationsForCurrentPointer,
      }}
    >
      {children}
    </ModelViewerContext.Provider>
  );
};

export const useModelViewer = () => useContext(ModelViewerContext);
