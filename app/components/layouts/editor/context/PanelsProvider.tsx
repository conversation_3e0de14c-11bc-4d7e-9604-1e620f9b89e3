'use client';

import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  useEffect,
} from 'react';
import {
  Pointer,
  PointerStyle,
  PointerPosition,
} from '@/app/components/layouts/editor';
import { useEditorCoordinator } from './EditorCoordinator';

interface PanelsContextType {
  // Pointer management
  pointers: Pointer[];
  selectedPointer: Pointer | null;
  isEditingPointer: boolean;
  hasUnsavedChanges: boolean;

  // Actions
  selectPointer: (pointer: Pointer) => void;
  createNewPointer: () => void;
  updateCurrentPointer: (updates: Partial<Pointer>) => void;
  updatePointerName: (name: string) => void;
  updatePointerDescription: (description: string) => void;
  updatePointerStyle: (style: Partial<PointerStyle>) => void;
  updatePointerPosition: (position: PointerPosition) => void;
  savePointer: () => void;
  deletePointer: (id: string) => void;
  cancelEditing: () => void;
}

const PanelsContext = createContext<PanelsContextType>({} as PanelsContextType);

const POINTERS_STORAGE_KEY = 'tnkr_pointers';

// Default style for new pointers
const DEFAULT_NEW_POINTER_STYLE: PointerStyle = {
  color: '#00FF00',
  borderStyle: 'solid',
  size: 'M',
  opacity: 100,
  borderWidth: '1px',
  font: 'Arial',
  alignment: 'left',
};

export const PanelsProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const { setCurrentPointerId, currentPointerId } = useEditorCoordinator();
  const [pointers, setPointers] = useState<Pointer[]>([]);
  const [selectedPointer, setSelectedPointer] = useState<Pointer | null>(null);
  const [originalPointer, setOriginalPointer] = useState<Pointer | null>(null);
  const [isEditingPointer, setIsEditingPointer] = useState(false);

  // Save current pointer to localStorage whenever it changes
  useEffect(() => {
    if (!currentPointerId || !selectedPointer) return;

    try {
      console.log('PanelsProvider: Saving current pointer to localStorage:', currentPointerId);
      
      // Get current stored pointers
      const stored = localStorage.getItem(POINTERS_STORAGE_KEY);
      let storedPointers: Pointer[] = [];
      if (stored) {
        storedPointers = JSON.parse(stored);
      }

      // Find if pointer already exists
      const existingIndex = storedPointers.findIndex(p => p.id === currentPointerId);
      
      // Prepare the pointer to save
      const pointerToSave = {
        ...selectedPointer,
        updatedAt: Date.now()
      };

      if (existingIndex >= 0) {
        // Update existing pointer, preserving annotations
        storedPointers[existingIndex] = {
          ...pointerToSave,
          annotations: storedPointers[existingIndex].annotations || pointerToSave.annotations
        };
      } else {
        // Add new pointer
        storedPointers.push(pointerToSave);
      }

      // Save back to localStorage
      localStorage.setItem(POINTERS_STORAGE_KEY, JSON.stringify(storedPointers));
      console.log('PanelsProvider: Saved pointer to localStorage:', {
        pointerId: currentPointerId,
        isNew: existingIndex === -1,
        totalPointers: storedPointers.length
      });

      // Update pointers state to match localStorage
      setPointers(storedPointers);
    } catch (error) {
      console.error('PanelsProvider: Error saving pointer to localStorage:', error);
    }
  }, [currentPointerId, selectedPointer]);

  // Load pointers from localStorage on mount
  useEffect(() => {
    const loadPointers = () => {
      try {
        const stored = localStorage.getItem(POINTERS_STORAGE_KEY);
        if (stored) {
          const parsedPointers = JSON.parse(stored);
          console.log('PanelsProvider: Loaded pointers from localStorage:', parsedPointers);
          setPointers(parsedPointers);

          // If we have a currentPointerId, set the selected pointer
          if (currentPointerId) {
            const currentPointer = parsedPointers.find((p: Pointer) => p.id === currentPointerId);
            if (currentPointer) {
              setSelectedPointer(currentPointer);
              setOriginalPointer(currentPointer);
              setIsEditingPointer(true);
            }
          }
        }
      } catch (error) {
        console.error('PanelsProvider: Error loading pointers:', error);
      }
    };
    loadPointers();
  }, [currentPointerId]);

  // Check for unsaved changes
  const hasUnsavedChanges = useCallback(() => {
    if (!selectedPointer || !originalPointer) {
      return false;
    }

    const hasNameChanged = selectedPointer.name !== originalPointer.name;
    const hasDescriptionChanged =
      selectedPointer.description !== originalPointer.description;
    const hasStyleChanged =
      JSON.stringify(selectedPointer.style) !==
      JSON.stringify(originalPointer.style);

    return hasNameChanged || hasDescriptionChanged || hasStyleChanged;
  }, [selectedPointer, originalPointer]);

  const selectPointer = useCallback(
    (pointer: Pointer) => {
      console.log('selected pointer', pointer);
      console.log('PanelsProvider: Selecting pointer:', pointer.id);
      setSelectedPointer({ ...pointer });
      setOriginalPointer({ ...pointer });
      setIsEditingPointer(true);
      setCurrentPointerId(pointer.id);
    },
    [setCurrentPointerId],
  );

  const createNewPointer = useCallback(() => {
    const newPointer: Pointer = {
      id: `pointer-${Date.now()}`,
      name: '',
      description: '',
      style: { ...DEFAULT_NEW_POINTER_STYLE },
      isNew: true,
      annotations: [],
      createdAt: Date.now(),
      updatedAt: Date.now(),
    };

    
    // Save to localStorage synchronously before setting currentPointerId
    try {
      const stored = localStorage.getItem(POINTERS_STORAGE_KEY);
      let storedPointers: Pointer[] = [];
      if (stored) {
        storedPointers = JSON.parse(stored);
      }
      storedPointers.push(newPointer);
      localStorage.setItem(POINTERS_STORAGE_KEY, JSON.stringify(storedPointers));
      console.log('PanelsProvider: Synchronously saved new pointer to localStorage');
    } catch (error) {
      console.error('PanelsProvider: Error saving new pointer to localStorage:', error);
    }

    // Then update state
    setSelectedPointer(newPointer);
    setOriginalPointer(newPointer);
    setIsEditingPointer(true);
    setCurrentPointerId(newPointer.id);
  }, [setCurrentPointerId]);

  const updateCurrentPointer = useCallback((updates: Partial<Pointer>) => {
    setSelectedPointer((prev) =>
      prev ? { ...prev, ...updates, updatedAt: Date.now() } : null,
    );
  }, []);

  const updatePointerName = useCallback(
    (name: string) => {
      updateCurrentPointer({ name });
    },
    [updateCurrentPointer],
  );

  const updatePointerDescription = useCallback(
    (description: string) => {
      updateCurrentPointer({ description });
    },
    [updateCurrentPointer],
  );

  const updatePointerStyle = useCallback(
    (style: Partial<PointerStyle>) => {
      updateCurrentPointer({
        style: selectedPointer?.style
          ? { ...selectedPointer.style, ...style }
          : { ...DEFAULT_NEW_POINTER_STYLE, ...style },
      });
    },
    [selectedPointer, updateCurrentPointer],
  );

  const updatePointerPosition = useCallback(
    (position: PointerPosition) => {
      updateCurrentPointer({ position });
    },
    [updateCurrentPointer],
  );

  const savePointer = useCallback(() => {
    if (!selectedPointer || !currentPointerId) return;

    console.log('PanelsProvider: Saving pointer:', currentPointerId);

    const finalPointer = {
      ...selectedPointer,
      isNew: false,
      updatedAt: Date.now(),
    };

    // Update state
    setSelectedPointer(finalPointer);
    setOriginalPointer(finalPointer);
    setPointers(prev => 
      prev.map((p: Pointer) => p.id === currentPointerId ? finalPointer : p)
    );
  }, [selectedPointer, currentPointerId]);

  const deletePointer = useCallback(
    (id: string) => {
      console.log('PanelsProvider: Deleting pointer:', id);
      
      // Update localStorage
      try {
        const stored = localStorage.getItem(POINTERS_STORAGE_KEY);
        if (stored) {
          const storedPointers = JSON.parse(stored);
          const updatedPointers = storedPointers.filter((p: Pointer) => p.id !== id);
          localStorage.setItem(POINTERS_STORAGE_KEY, JSON.stringify(updatedPointers));
          setPointers(updatedPointers);
        }
      } catch (error) {
        console.error('PanelsProvider: Error deleting pointer from localStorage:', error);
      }

      if (selectedPointer?.id === id) {
        setSelectedPointer(null);
        setOriginalPointer(null);
        setIsEditingPointer(false);
        setCurrentPointerId(null);
      }
    },
    [selectedPointer, setCurrentPointerId],
  );

  const cancelEditing = useCallback(() => {
    console.log('PanelsProvider: Canceling editing');
    setSelectedPointer(null);
    setOriginalPointer(null);
    setIsEditingPointer(false);
    setCurrentPointerId(null);
  }, [setCurrentPointerId]);

  return (
    <PanelsContext.Provider
      value={{
        pointers,
        selectedPointer,
        isEditingPointer,
        hasUnsavedChanges: hasUnsavedChanges(),
        selectPointer,
        createNewPointer,
        updateCurrentPointer,
        updatePointerName,
        updatePointerDescription,
        updatePointerStyle,
        updatePointerPosition,
        savePointer,
        deletePointer,
        cancelEditing,
      }}
    >
      {children}
    </PanelsContext.Provider>
  );
};

export const usePanels = () => useContext(PanelsContext);
