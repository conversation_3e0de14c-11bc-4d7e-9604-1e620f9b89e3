'use client';

import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  ReactNode,
} from 'react';
import { Part, BOMItem, EditorContextType } from '../types';

// Create context with default values
const EditorContext = createContext<EditorContextType>({
  parts: [],
  bomItems: [],
  selectedPartId: null,
  setParts: () => {},
  setBOMItems: () => {},
  setSelectedPartId: () => {},
  updatePart: () => {},
  updateBOMItem: () => {},
  findPartById: () => null,
});

interface EditorProviderProps {
  children: ReactNode;
  initialParts?: Part[];
  initialBOMItems?: BOMItem[];
  onPartsChange?: (parts: Part[]) => void;
  onBOMChange?: (bomItems: BOMItem[]) => void;
}

export const EditorProvider: React.FC<EditorProviderProps> = ({
  children,
  initialParts = [],
  initialBOMItems = [],
  onPartsChange,
  onBOMChange,
}) => {
  const [parts, setParts] = useState<Part[]>([]);
  const [bomItems, setBOMItems] = useState<BOMItem[]>(initialBOMItems);
  const [selectedPartId, setSelectedPartId] = useState<string | null>(null);

  // Handler for updating parts with callback to parent
  const handleSetParts = useCallback(
    (newParts: Part[]) => {
      setParts(newParts);
      if (onPartsChange) {
        onPartsChange(newParts);
      }
    },
    [onPartsChange],
  );

  // Handler for updating BOM items with callback to parent
  const handleSetBOMItems = useCallback(
    (newBOMItems: BOMItem[]) => {
      setBOMItems(newBOMItems);
      if (onBOMChange) {
        onBOMChange(newBOMItems);
      }
    },
    [onBOMChange],
  );

  // Find a part by ID
  const findPartById = useCallback(
    (id: string): Part | null => {
      const findPart = (partsArray: Part[]): Part | null => {
        for (const part of partsArray) {
          if (part.id === id) {
            return part;
          }
          if (part.children) {
            const found = findPart(part.children);
            if (found) {
              return found;
            }
          }
        }
        return null;
      };

      return findPart(parts);
    },
    [parts],
  );

  // Update a part in the tree
  const updatePart = useCallback(
    (id: string, updates: Partial<Part>) => {
      const updatePartInTree = (partsArray: Part[]): Part[] => {
        return partsArray.map((part) => {
          if (part.id === id) {
            return { ...part, ...updates };
          }
          if (part.children) {
            return {
              ...part,
              children: updatePartInTree(part.children),
            };
          }
          return part;
        });
      };

      const updatedParts = updatePartInTree(parts);
      handleSetParts(updatedParts);
    },
    [parts, handleSetParts],
  );

  // Update a BOM item
  const updateBOMItem = useCallback(
    (id: string, updates: Partial<BOMItem>) => {
      const updatedBOMItems = bomItems.map((item) =>
        item.id === id ? { ...item, ...updates } : item,
      );
      handleSetBOMItems(updatedBOMItems);
    },
    [bomItems, handleSetBOMItems],
  );

  const value = {
    parts,
    bomItems,
    selectedPartId,
    setParts: handleSetParts,
    setBOMItems: handleSetBOMItems,
    setSelectedPartId,
    updatePart,
    updateBOMItem,
    findPartById,
  };

  return (
    <EditorContext.Provider value={value}>{children}</EditorContext.Provider>
  );
};

// Hook for using the editor context
export const useEditor = () => useContext(EditorContext);
