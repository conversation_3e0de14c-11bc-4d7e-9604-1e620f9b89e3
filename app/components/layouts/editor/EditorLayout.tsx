'use client';

import React from 'react';
import { EditorProvider } from './context/EditorContext';
import { EditorTabPanel } from './EditorTabPanel';
import { EditorLayoutProps } from './types';

const EditorLayout: React.FC<EditorLayoutProps> = ({
  leftRegion,
  rightRegion,
  centerContent,
  initialParts = [],
  initialBOMItems = [],
  onPartsChange,
  onBOMChange,
}) => {
  // Set default widths if not provided
  const leftWidth = leftRegion.width || 250;
  const rightWidth = rightRegion.width || 300;

  return (
    <EditorProvider
      initialParts={initialParts}
      initialBOMItems={initialBOMItems}
      onPartsChange={onPartsChange}
      onBOMChange={onBOMChange}
    >
      <div className='flex h-full text-white'>
        {/* Left Region */}
        <div
          className='border-r border-[#313133] flex flex-col overflow-hidden'
          style={{ width: leftWidth }}
        >
          {leftRegion.tabs && (
            <EditorTabPanel
              tabs={leftRegion.tabs}
              defaultTabId={leftRegion.defaultTabId}
              className='h-full'
            />
          )}
          {leftRegion.content && leftRegion.content}
        </div>

        {/* Center Content Area */}
        <div className='flex-1 flex flex-col overflow-hidden'>
          {centerContent || (
            <div className='flex items-center justify-center h-full'>
              <p className='text-white/50'>
                Content area for 3D model or documentation
              </p>
            </div>
          )}
        </div>

        {/* Right Region */}
        <div
          className='border-l border-[#313133] flex flex-col overflow-hidden'
          style={{ width: rightWidth }}
        >
          {rightRegion.tabs && rightRegion.tabs.length > 0 ? (
            <EditorTabPanel
              tabs={rightRegion.tabs}
              defaultTabId={rightRegion.defaultTabId}
              className='h-full'
            />
          ) : (
            // Custom content for when there are no tabs
            rightRegion.content || (
              <div className='flex items-center justify-center h-full'>
                <p className='text-white/50'>No content available</p>
              </div>
            )
          )}
        </div>
      </div>
    </EditorProvider>
  );
};

export default EditorLayout;
