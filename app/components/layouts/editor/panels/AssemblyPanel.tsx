import React from 'react';
import { EditorPanel } from '../EditorPanel';

interface AssemblyPanelProps {
  id: string;
  title: string;
}

export const AssemblyPanel: React.FC<AssemblyPanelProps> = ({
                                                              id,
                                                              title,
                                                            }) => {
  return (
    <EditorPanel
      id={id}
      title={title}
      className='flex-1'
    >
      <div className='flex items-center justify-center h-full'>
        <p className='text-white/50'>Assembly view - Coming soon</p>
      </div>
    </EditorPanel>
  );
};

export default AssemblyPanel;