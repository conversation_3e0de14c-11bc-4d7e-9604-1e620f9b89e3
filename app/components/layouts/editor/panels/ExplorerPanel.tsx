import React, { useState, useCallback } from 'react';
import { Plus } from 'lucide-react';
import { Button } from '@/app/components/ui/Button';
import { EditorPanel } from '../EditorPanel';
import { useParts } from '../hooks/useParts';
import { useBOM } from '../hooks/useBOM';
import PartTreeItem from '../components/PartTreeItem';
import BOMItemComponent from '../components/BOMItem';
import { useModelViewCore } from '../ModelViewCore/context/ModelViewCoreContext';

interface ExplorerPanelProps {
  id: string;
  title?: string; // Panel title is optional since we're using internal section titles
  partsTreeTitle?: string;
  bomTitle?: string;
  enablePartsSearch?: boolean;
  enableBOMSearch?: boolean;
  defaultPartsCollapsed?: boolean;
  defaultBOMCollapsed?: boolean;
  enableAddPart?: boolean;
  onAddPart?: () => void;
}

export const ExplorerPanel: React.FC<ExplorerPanelProps> = ({
  id,
  partsTreeTitle = 'Parts Tree',
  bomTitle = 'Bill of Materials',
  enablePartsSearch = true,
  enableBOMSearch = true,
  defaultPartsCollapsed = false,
  defaultBOMCollapsed = false,
  enableAddPart = true,
  onAddPart,
}) => {
  // Parts tree state
  const { parts, selectedPartId, setSelectedPartId, searchParts, movePart } =
    useParts();
  const { controlFunctions } = useModelViewCore();

  const [partsSearchTerm, setPartsSearchTerm] = useState('');
  const [dragInfo, setDragInfo] = useState<{
    id: string;
    level: number;
  } | null>(null);
  const [dropTargetId, setDropTargetId] = useState<string | null>(null);
  const [partsCollapsed] = useState(defaultPartsCollapsed);

  // BOM state
  const { bomItems, searchBOMItems, updateQuantity } = useBOM();
  const [bomSearchTerm, setBomSearchTerm] = useState('');
  const [bomCollapsed] = useState(defaultBOMCollapsed);

  // Filter parts based on search term
  const filteredParts = partsSearchTerm ? searchParts(partsSearchTerm) : parts;

  // Filter BOM items by search term
  const filteredBOMItems = bomSearchTerm
    ? searchBOMItems(bomSearchTerm)
    : bomItems;

  // Handle parts search
  const handlePartsSearch = useCallback((term: string) => {
    setPartsSearchTerm(term);
  }, []);

  // Handle BOM search
  const handleBOMSearch = useCallback((term: string) => {
    setBomSearchTerm(term);
  }, []);

  // Handle drag start
  const handleDragStart = (e: React.DragEvent, id: string, level: number) => {
    setDragInfo({ id, level });
    e.dataTransfer.setData('text/plain', id);
  };

  // Handle drag over
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  // Handle drag enter
  const handleDragEnter = (e: React.DragEvent, id: string) => {
    e.preventDefault();
    if (dragInfo && dragInfo.id !== id) {
      setDropTargetId(id);
    }
  };

  // Handle drag leave
  const handleDragLeave = (e: React.DragEvent, id: string) => {
    e.preventDefault();
    if (dropTargetId === id) {
      setDropTargetId(null);
    }
  };

  // Handle drop
  const handleDrop = (e: React.DragEvent, targetId: string) => {
    e.preventDefault();
    setDropTargetId(null);

    if (!dragInfo || dragInfo.id === targetId) {
      return;
    }

    // Use the movePart function from useParts hook
    movePart(dragInfo.id, targetId);

    setDragInfo(null);
  };

  // Handle part selection with highlighting
  const handlePartSelect = useCallback(
    (partId: string) => {
      // Remove highlight from previously selected part
      if (selectedPartId) {
        controlFunctions?.partOperations.removeHighlight(selectedPartId);
      }

      // Set new selected part and highlight it
      setSelectedPartId(partId);
      if (partId) {
        controlFunctions?.partOperations.highlightPart(partId);
      }
    },
    [selectedPartId, setSelectedPartId, controlFunctions?.partOperations],
  );

  // Render the add button if enabled
  const renderAddButton = useCallback(() => {
    if (enableAddPart) {
      return (
        <div className='rounded-full flex items-center justify-center w-4 h-4 bg-white/70'>
          <Button
            size='icon'
            variant='ghost'
            className='p-1 text-[#313133]'
            onClick={onAddPart}
            title='Add new part'
          >
            <Plus size={12} />
          </Button>
        </div>
      );
    }
    return null;
  }, [enableAddPart, onAddPart]);

  return (
    <div className='flex flex-col h-full'>
      {/* Parts Tree Section */}
      <div className='flex flex-col flex-1 min-h-0'>
        <EditorPanel
          id={`${id}-parts-tree`}
          title={partsTreeTitle}
          enableSearch={enablePartsSearch}
          defaultCollapsed={partsCollapsed}
          onSearch={handlePartsSearch}
          headerRightContent={renderAddButton()}
          className='flex-1 min-h-0 flex flex-col'
        >
          <div className='flex-1 overflow-y-auto min-h-0'>
            {filteredParts.length > 0 ? (
              filteredParts.map((part) => (
                <PartTreeItem
                  key={part.id}
                  part={part}
                  level={0}
                  selectedId={selectedPartId}
                  onSelect={handlePartSelect}
                  onDragStart={handleDragStart}
                  onDrop={handleDrop}
                  onDragOver={handleDragOver}
                  onDragEnter={handleDragEnter}
                  onDragLeave={handleDragLeave}
                  isDragTarget={dropTargetId === part.id}
                />
              ))
            ) : (
              <div className='flex items-center justify-center p-4'>
                <p className='text-white/50 text-sm'>No parts found</p>
              </div>
            )}
          </div>
        </EditorPanel>
      </div>

      {/* BOM Section */}
      <div className='border-t border-b border-[#313133] flex flex-col min-h-0 flex-1'>
        <EditorPanel
          id={`${id}-bom`}
          title={bomTitle}
          enableSearch={enableBOMSearch}
          defaultCollapsed={bomCollapsed}
          onSearch={handleBOMSearch}
          className='flex-1 min-h-0 flex mt-2 flex-col'
        >
          <div className='overflow-y-auto px-3 mt-1 flex-1 min-h-0'>
            {filteredBOMItems.length > 0 ? (
              filteredBOMItems.map((item) => (
                <BOMItemComponent
                  key={item.id}
                  item={item}
                  onQuantityChange={updateQuantity}
                />
              ))
            ) : (
              <div className='flex items-center justify-center p-4'>
                <p className='text-white/50 text-sm'>No materials added</p>
              </div>
            )}
          </div>
        </EditorPanel>
      </div>
    </div>
  );
};

export default ExplorerPanel;
