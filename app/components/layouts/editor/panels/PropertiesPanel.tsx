// File: components/layouts/editor/panels/PropertiesPanel.tsx
import React from 'react';
import { Button } from '@/app/components/ui/Button';
import { EditorPanel } from '../EditorPanel';
import { useParts } from '../hooks/useParts';
import { PartCategory } from '../types';
import PropertySection from '../components/PropertySection';
import DynamicField from '../components/DynamicField';

// Helper to get specifications based on part category
const getSpecificationFields = (
  category?: PartCategory,
): { [key: string]: string } => {
  switch (category) {
    case 'Motors':
      return {
        type: 'Dropdown',
        torque: 'Numeric',
        rpm: 'Numeric',
        voltage: 'Numeric',
        current: 'Numeric',
        weight: 'Numeric',
      };
    case '3D Printed Parts':
      return {
        material: 'Dropdown',
        layerHeight: 'Numeric',
        infillPercentage: 'Numeric',
        dimensions: 'Dimensions',
        printerUsed: 'Text',
        printTime: 'Text',
      };
    case 'Electronics':
      return {
        type: 'Dropdown',
        voltage: 'Numeric',
        current: 'Numeric',
        operatingTemperature: 'Range',
        pinCount: 'Numeric',
        dimensions: 'Dimensions',
      };
    case 'Fasteners':
      return {
        type: 'Dropdown',
        threadSize: 'Text',
        length: 'Numeric',
        material: 'Dropdown',
        headType: 'Dropdown',
        torqueRequirement: 'Numeric',
      };
    case 'Structural':
      return {
        material: 'Dropdown',
        dimensions: 'Dimensions',
        weight: 'Numeric',
        loadCapacity: 'Numeric',
        surfaceFinish: 'Text',
      };
    default:
      return {};
  }
};

interface PropertiesPanelProps {
  id: string;
  title: string;
}

export const PropertiesPanel: React.FC<PropertiesPanelProps> = ({
                                                                  id,
                                                                  title,
                                                                }) => {
  const { selectedPart, updatePart } = useParts();

  // Handler for updating part properties
  const handlePropertyChange = (property: string, value: any) => {
    if (!selectedPart) return;

    if (
      property === 'name' ||
      property === 'description' ||
      property === 'partNumber' ||
      property === 'category' ||
      property === 'type'
    ) {
      updatePart(selectedPart.id, {
        [property]: value,
      });
    } else if (property.startsWith('spec.')) {
      const specName = property.replace('spec.', '');
      updatePart(selectedPart.id, {
        specifications: {
          ...selectedPart.specifications,
          [specName]: value,
        },
      });
    } else if (property.startsWith('assembly.')) {
      const assemblyProperty = property.replace('assembly.', '');
      updatePart(selectedPart.id, {
        assembly: {
          ...selectedPart.assembly,
          [assemblyProperty]: value,
        },
      });
    } else if (property.startsWith('externalLinks.')) {
      const linkProperty = property.replace('externalLinks.', '');
      updatePart(selectedPart.id, {
        externalLinks: {
          ...selectedPart.externalLinks,
          [linkProperty]: value,
        },
      });
    } else if (property.startsWith('customField.')) {
      const fieldName = property.replace('customField.', '');
      updatePart(selectedPart.id, {
        customFields: {
          ...selectedPart.customFields,
          [fieldName]: value,
        },
      });
    }
  };

  // Get dropdown options based on field
  const getOptionsForField = (field: string, category?: PartCategory) => {
    if (field === 'category') {
      return [
        { value: 'Motors', label: 'Motors' },
        { value: '3D Printed Parts', label: '3D Printed Parts' },
        { value: 'Electronics', label: 'Electronics' },
        { value: 'Fasteners', label: 'Fasteners' },
        { value: 'Structural', label: 'Structural' },
      ];
    }

    if (field === 'type' && category === 'Motors') {
      return [
        { value: 'Stepper', label: 'Stepper' },
        { value: 'Servo', label: 'Servo' },
        { value: 'DC', label: 'DC' },
      ];
    }

    if (field === 'material' && category === '3D Printed Parts') {
      return [
        { value: 'PLA', label: 'PLA' },
        { value: 'ABS', label: 'ABS' },
        { value: 'PETG', label: 'PETG' },
      ];
    }

    if (field === 'type' && category === 'Electronics') {
      return [
        { value: 'PCB', label: 'PCB' },
        { value: 'Sensor', label: 'Sensor' },
        { value: 'Microcontroller', label: 'Microcontroller' },
      ];
    }

    if (field === 'type' && category === 'Fasteners') {
      return [
        { value: 'Screw', label: 'Screw' },
        { value: 'Bolt', label: 'Bolt' },
        { value: 'Nut', label: 'Nut' },
      ];
    }

    if (field === 'material' && category === 'Structural') {
      return [
        { value: 'Aluminum', label: 'Aluminum' },
        { value: 'Steel', label: 'Steel' },
        { value: 'Plastic', label: 'Plastic' },
      ];
    }

    return [];
  };

  // Render properties panel content
  const renderPropertiesPanelContent = () => {
    if (!selectedPart) {
      return (
        <div className='flex items-center justify-center h-full'>
          <p className='text-white/50'>Select a part to edit properties</p>
        </div>
      );
    }

    const specFields = getSpecificationFields(selectedPart.category);

    return (
      <div className='px-4 space-y-6 overflow-y-auto h-full'>
        <PropertySection title='General Information'>
          <div className='grid grid-cols-1 gap-4'>
            <DynamicField
              label='Name'
              type='Text'
              value={selectedPart.name}
              onChange={(value) => handlePropertyChange('name', value)}
            />
            <DynamicField
              label='Part Number'
              type='Text'
              value={selectedPart.partNumber}
              onChange={(value) => handlePropertyChange('partNumber', value)}
            />
            <DynamicField
              label='Category'
              type='Dropdown'
              value={selectedPart.category}
              onChange={(value) => handlePropertyChange('category', value)}
              options={getOptionsForField('category')}
            />
            <DynamicField
              label='Description'
              type='TextArea'
              value={selectedPart.description}
              onChange={(value) => handlePropertyChange('description', value)}
            />
          </div>
        </PropertySection>

        {selectedPart.category && (
          <PropertySection title='Technical Specs'>
            <div className='grid grid-cols-1 gap-4'>
              {Object.entries(specFields).map(([key, fieldType]) => (
                <DynamicField
                  key={key}
                  label={key.charAt(0).toUpperCase() + key.slice(1)}
                  type={fieldType}
                  value={selectedPart.specifications[key]}
                  onChange={(value) =>
                    handlePropertyChange(`spec.${key}`, value)
                  }
                  options={getOptionsForField(key, selectedPart.category)}
                />
              ))}
            </div>
          </PropertySection>
        )}

        <PropertySection title='Assembly & Maintenance'>
          <div className='grid grid-cols-1 gap-4'>
            <DynamicField
              label='Assembly Notes'
              type='TextArea'
              value={selectedPart.assembly?.notes}
              onChange={(value) =>
                handlePropertyChange('assembly.notes', value)
              }
            />
            <DynamicField
              label='Maintenance Schedule'
              type='Text'
              value={selectedPart.assembly?.maintenanceSchedule}
              onChange={(value) =>
                handlePropertyChange('assembly.maintenanceSchedule', value)
              }
            />
            <DynamicField
              label='Known Issues'
              type='TextArea'
              value={selectedPart.assembly?.knownIssues}
              onChange={(value) =>
                handlePropertyChange('assembly.knownIssues', value)
              }
            />
          </div>
        </PropertySection>

        <PropertySection title='External Links'>
          <div className='grid grid-cols-1 gap-4'>
            <DynamicField
              label='Datasheet URL'
              type='Text'
              value={selectedPart.externalLinks?.datasheetUrl}
              onChange={(value) =>
                handlePropertyChange('externalLinks.datasheetUrl', value)
              }
            />
            <DynamicField
              label='Supplier URL'
              type='Text'
              value={selectedPart.externalLinks?.supplierUrl}
              onChange={(value) =>
                handlePropertyChange('externalLinks.supplierUrl', value)
              }
            />
          </div>
        </PropertySection>

        <PropertySection title='Custom Fields'>
          <div className='grid grid-cols-1 gap-4'>
            {selectedPart.customFields &&
              Object.entries(selectedPart.customFields).map(([key, value]) => (
                <div key={key} className='grid grid-cols-2 gap-2'>
                  <DynamicField
                    label='Key'
                    type='Text'
                    value={key}
                    onChange={(newKey) => {
                      const newCustomFields = { ...selectedPart.customFields };
                      delete newCustomFields[key];
                      newCustomFields[newKey] = value;
                      updatePart(selectedPart.id, {
                        customFields: newCustomFields
                      });
                    }}
                  />
                  <DynamicField
                    label='Value'
                    type='Text'
                    value={value}
                    onChange={(newValue) =>
                      handlePropertyChange(`customField.${key}`, newValue)
                    }
                  />
                </div>
              ))}
            <Button
              variant='outline'
              className='mt-2'
              onClick={() => {
                const newCustomFields = {
                  ...(selectedPart.customFields || {}),
                };
                newCustomFields[
                  `field${Object.keys(newCustomFields).length + 1}`
                  ] = '';
                updatePart(selectedPart.id, {
                  customFields: newCustomFields
                });
              }}
            >
              Add Custom Field
            </Button>
          </div>
        </PropertySection>
      </div>
    );
  };

  return (
    <EditorPanel
      id={id}
      title={title}
      className='flex-1 overflow-auto'
    >
      {renderPropertiesPanelContent()}
    </EditorPanel>
  );
};

export default PropertiesPanel;