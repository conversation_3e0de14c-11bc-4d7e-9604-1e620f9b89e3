'use client';

import React from 'react';
import { EditorPanel } from '../EditorPanel';
import { Button } from '@/app/components/ui/Button';
import { Icon } from '@iconify/react';
import { usePanels } from '../context/PanelsProvider';
import { Pointer } from '@/app/components/layouts/editor';

interface PointersPanelProps {
  id: string;
  title: string;
  pointers?: Pointer[]; // Keep for backward compatibility but use context
}

export const PointersPanel: React.FC<PointersPanelProps> = ({ id, title }) => {
  const {
    pointers,
    selectedPointer,
    isEditingPointer,
    selectPointer,
    createNewPointer,
    deletePointer,
  } = usePanels();

  const handleSelectPointer = (pointer: Pointer) => {
    selectPointer(pointer);
  };

  const handleAddPointer = () => {
    createNewPointer();
  };

  const handleDeletePointer = (e: React.MouseEvent, pointerId: string) => {
    e.stopPropagation(); // Prevent triggering the pointer selection
    if (window.confirm('Are you sure you want to delete this pointer?')) {
      deletePointer(pointerId);
    }
  };

  return (
    <EditorPanel
      id={id}
      title={title}
      className='flex-1 flex flex-col'
      showTitle={false}
    >
      <div className='flex-1 overflow-y-auto px-4'>
        {pointers.length > 0 && (
          <div className=''>
            {pointers.map((pointer) => (
              <div
                key={pointer.id}
                className={`p-4 rounded-lg cursor-pointer ${
                  selectedPointer &&
                  selectedPointer.id === pointer.id &&
                  isEditingPointer
                    ? 'bg-[#AA423A]/20'
                    : ' hover:bg-[#313133]'
                }`}
                onClick={() => handleSelectPointer(pointer)}
              >
                <div className='flex items-center justify-between gap-3'>
                  <div className='flex items-center gap-3'>
                    <div className='bg-[#161617] flex items-center p-2 rounded-lg'>
                      <Icon icon='tabler:pointer-plus' color='#AA423A' />
                    </div>
                    <span className='text-white/70 text-md'>{pointer.name}</span>
                  </div>
                  <button
                    onClick={(e) => handleDeletePointer(e, pointer.id)}
                    className='p-2 hover:bg-[#AA423A]/20 rounded-lg transition-colors'
                    title='Delete pointer'
                  >
                    <Icon icon='tabler:trash' className='text-white/50 hover:text-[#AA423A]' />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className='p-4 mt-auto'>
        <Button
          variant='outline'
          className='w-full border-2 bg-[#161617] border-dashed border-[#ffffff]/70 py-6 rounded-lg cursor-pointer hover:bg-[#232326]'
          onClick={handleAddPointer}
        >
          <Icon icon='tabler:pointer-plus' />
          Add a New Pointer
        </Button>
      </div>
    </EditorPanel>
  );
};

export default PointersPanel;
