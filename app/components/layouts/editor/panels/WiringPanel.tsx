import React from 'react';
import { EditorPanel } from '../EditorPanel';

interface WiringPanelProps {
  id: string;
  title: string;
}

export const WiringPanel: React.FC<WiringPanelProps> = ({
                                                          id,
                                                          title,
                                                        }) => {
  return (
    <EditorPanel
      id={id}
      title={title}
      className='flex-1'
    >
      <div className='flex items-center justify-center h-full'>
        <p className='text-white/50'>Wiring diagram - Coming soon</p>
      </div>
    </EditorPanel>
  );
};

export default WiringPanel;