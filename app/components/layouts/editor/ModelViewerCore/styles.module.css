/*
  styles.module.css - Styles for the ModelViewerCore component
*/

.container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    border-radius: 8px;
}

.canvas {
    width: 100%;
    height: 100%;
    display: block;
}

.loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #ffffff;
    font-size: 16px;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 10px 20px;
    border-radius: 5px;
    z-index: 10;
}

.error {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #ff5555;
    background-color: rgba(0, 0, 0, 0.7);
    padding: 15px;
    border-radius: 8px;
    max-width: 80%;
    text-align: center;
    z-index: 10;
}

/* Annotations shouldn't be selectable with mouse */
.container * {
    user-select: none;
    -webkit-user-select: none;
}

/* Styling for the grid */
.grid {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

/* Active mouse tool cursors */
.containerPen {
    cursor: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 19l7-7 3 3-7 7-3-3z'%3E%3C/path%3E%3Cpath d='M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z'%3E%3C/path%3E%3Cpath d='M2 2l7.586 7.586'%3E%3C/path%3E%3Ccircle cx='11' cy='11' r='2'%3E%3C/circle%3E%3C/svg%3E") 0 24, auto;
}

.containerMove {
    cursor: grab;
}

.containerMoveDragging {
    cursor: grabbing;
}

.containerZoom {
    cursor: zoom-in;
}

.containerShape {
    cursor: crosshair;
}

.containerText {
    cursor: text;
}

/* Annotations */
.annotation {
    pointer-events: all;
    cursor: move;
    user-select: none;
    white-space: nowrap;
    min-width: 30px;
    min-height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

/* Parts highlighting on hover */
.highlight {
    outline: 2px solid #ffffff;
    outline-offset: 2px;
}

.annotation-text {
    cursor: move !important;
    user-select: none;
}

.annotation-text[contenteditable="true"] {
    cursor: text !important;
    outline: 2px solid rgba(255, 255, 255, 0.5);
}