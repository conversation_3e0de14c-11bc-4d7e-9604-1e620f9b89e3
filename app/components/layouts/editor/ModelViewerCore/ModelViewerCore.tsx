import React, { useEffect, useRef, useState, useCallback } from 'react';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { CSS3DRenderer } from 'three/examples/jsm/renderers/CSS3DRenderer.js';
import useModelInteraction from './useModelInteraction';
import useAnnotations from './useAnnotations';
import { ModelViewerCoreProps, Annotation, SavedAnnotation } from './types';
import styles from './styles.module.css';
import PartContextMenu from '../ModelViewCore/context/PartContextMenu';
import { useModelViewer } from '../context/ModelViewerProvider';

const ModelViewerCore: React.FC<ModelViewerCoreProps> = ({
  modelPath,
  className = '',
  selectedTool,
  style,
  selectedPartName,
  onPartSelect,
  multiAnnotationsEnabled = true,
  onModelLoaded,
  onError,
  backgroundColor = '#0D0D0D',
  initialZoom = 1.0,
  showGrid = false,
  onAnnotationsChange,
  currentPointerId,
}) => {
  const modelViewerContext = useModelViewer?.();

  // Refs for Three.js objects
  const containerRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const cameraRef = useRef<THREE.PerspectiveCamera | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const cssRendererRef = useRef<CSS3DRenderer | null>(null);
  const controlsRef = useRef<OrbitControls | null>(null);
  const lastShapeRef = useRef<string>('default');
  const resizeObserverRef = useRef<ResizeObserver | null>(null);

  // State for loading and errors
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [, setSelectedAnnotation] = useState<Annotation | null>(null);

  const lastPointerIdRef = useRef<string>('');

  // State for isolated parts (keeping this as it was working)
  const [isolatedPartName, setIsolatedPartName] = useState<string | null>(null);

  // Custom hook for model interactions (destructure highlightComponent and resetHighlight)
  const {
    selectComponent,
    performRaycasting,
    highlightComponent, // Restored from hook
    resetHighlight, // Restored from hook
    processComponent,
    resetComponents,
    componentsRef,
  } = useModelInteraction({
    sceneRef,
    cameraRef,
    rendererRef,
    containerRef,
  });

  // Custom hook for annotations
  const {
    createAnnotation,
    clearAnnotations,
    updateAnnotation,
    updateAnnotationOrientations,
    makeAnnotationDraggable,
    findClosestAnnotation,
    getCurrentAnnotations,
    loadAnnotations,
  } = useAnnotations({
    sceneRef,
    cameraRef,
    containerRef,
    multiAnnotationsEnabled,
    currentPointerId: currentPointerId || undefined,
  });

  // Function to get current annotations for external use
  const getAnnotationsForExternal = useCallback((): SavedAnnotation[] => {
    const annotations = getCurrentAnnotations();
    console.log(
      'ModelViewerCore: Getting annotations for external use:',
      annotations,
    );

    // Save to context if available

    return annotations;
  }, [getCurrentAnnotations, currentPointerId, modelViewerContext]);

  const getAnnotationsToLoad = useCallback((): SavedAnnotation[] => {
    if (currentPointerId && modelViewerContext) {
      const contextAnnotations =
        modelViewerContext.getAnnotationsForCurrentPointer();
      console.log(
        `ModelViewerCore: Loading ${contextAnnotations.length} annotations from context for pointer ${currentPointerId}`,
      );
      return contextAnnotations;
    }

    console.log('ModelViewerCore: No annotations to load');
    return [];
  }, [currentPointerId, modelViewerContext]);

  // Handle resize of container
  const handleResize = useCallback(() => {
    if (
      !containerRef.current ||
      !cameraRef.current ||
      !rendererRef.current ||
      !cssRendererRef.current
    )
      return;

    const { width, height } = containerRef.current.getBoundingClientRect();

    // Update camera aspect ratio
    cameraRef.current.aspect = width / height;
    cameraRef.current.updateProjectionMatrix();

    // Resize renderers
    rendererRef.current.setSize(width, height);
    cssRendererRef.current.setSize(width, height);

    // Force a render to update the view
    if (sceneRef.current && cameraRef.current) {
      rendererRef.current.render(sceneRef.current, cameraRef.current);
      cssRendererRef.current.render(sceneRef.current, cameraRef.current);
    }

    console.log('ModelViewerCore resized:', width, 'x', height);
  }, []);

  // Update lastShapeRef when selected tool changes
  useEffect(() => {
    if (selectedTool?.subTool?.includes('shape-')) {
      const shape = selectedTool.subTool.replace('shape-', '');
      lastShapeRef.current = shape;
    }
  }, [selectedTool]);

  useEffect(() => {
    if (onAnnotationsChange) {
      console.log(
        'ModelViewerCore: Registering annotations getter with parent',
      );
      onAnnotationsChange(getAnnotationsForExternal);
    }
  }, [onAnnotationsChange, getAnnotationsForExternal]);

  // Initialize Three.js scene
  useEffect(() => {
    if (!containerRef.current) return;

    // Setup scene
    sceneRef.current = new THREE.Scene();
    sceneRef.current.background = new THREE.Color(backgroundColor);

    const container = containerRef.current;
    const { width, height } = container.getBoundingClientRect();

    // Setup camera
    cameraRef.current = new THREE.PerspectiveCamera(
      75,
      width / height,
      0.1,
      1000,
    );
    cameraRef.current.position.set(0, 2, 5);

    // Setup WebGL renderer
    rendererRef.current = new THREE.WebGLRenderer({ antialias: true });
    rendererRef.current.setSize(width, height);
    rendererRef.current.setPixelRatio(window.devicePixelRatio);
    // Use legacy lights setting instead of deprecated physicallyCorrectLights
    (rendererRef.current as any).useLegacyLights = false;
    rendererRef.current.toneMapping = THREE.ACESFilmicToneMapping;
    rendererRef.current.toneMappingExposure = 1.0;
    rendererRef.current.shadowMap.enabled = true;
    container.appendChild(rendererRef.current.domElement);

    // Setup CSS3D renderer for annotations
    cssRendererRef.current = new CSS3DRenderer();
    cssRendererRef.current.setSize(width, height);
    cssRendererRef.current.domElement.style.position = 'absolute';
    cssRendererRef.current.domElement.style.top = '0px';
    cssRendererRef.current.domElement.style.pointerEvents = 'none';
    container.appendChild(cssRendererRef.current.domElement);

    // Setup controls
    controlsRef.current = new OrbitControls(
      cameraRef.current,
      rendererRef.current.domElement,
    );
    controlsRef.current.enableDamping = true;
    controlsRef.current.dampingFactor = 0.05;

    // Add lights
    addLights();

    // Add grid if enabled
    if (showGrid) {
      addGrid();
    }

    // Start animation loop
    const animate = () => {
      if (
        !sceneRef.current ||
        !cameraRef.current ||
        !rendererRef.current ||
        !cssRendererRef.current
      ) {
        return;
      }

      requestAnimationFrame(animate);

      // Update controls
      controlsRef.current?.update();

      // Update annotation orientations to face camera
      updateAnnotationOrientations();

      // Render
      rendererRef.current.render(sceneRef.current, cameraRef.current);
      cssRendererRef.current.render(sceneRef.current, cameraRef.current);
    };

    animate();

    // Load the model
    loadModel(modelPath);

    // Setup ResizeObserver to detect container size changes
    resizeObserverRef.current = new ResizeObserver(handleResize);
    resizeObserverRef.current.observe(container);

    // Handle window resize
    window.addEventListener('resize', handleResize);

    // Cleanup function
    return () => {
      window.removeEventListener('resize', handleResize);

      // Disconnect ResizeObserver
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }

      if (containerRef.current) {
        if (rendererRef.current) {
          containerRef.current.removeChild(rendererRef.current.domElement);
          rendererRef.current.dispose();
        }

        if (cssRendererRef.current) {
          containerRef.current.removeChild(cssRendererRef.current.domElement);
        }
      }

      // Dispose of Three.js resources
      if (sceneRef.current) {
        disposeScene(sceneRef.current);
      }
    };
  }, [backgroundColor, showGrid, handleResize]);

  useEffect(() => {
    // Only handle annotation changes if the model is already loaded
    if (isLoading) {
      console.log('Model not loaded yet, skipping annotation handling');
      return;
    }
    clearAnnotations();

    if (currentPointerId) {
      // Get the annotations to load for this pointer first
      const annotationsToLoad = getAnnotationsToLoad();
      console.log(
        `Found ${annotationsToLoad.length} annotations to load for pointer: ${currentPointerId}`,
      );

      // Only proceed if we have annotations to load or explicitly no annotations
      if (annotationsToLoad.length >= 1) {
        // Small delay to ensure scene is ready
        const timer = setTimeout(() => {
          // Clear existing annotations only after we have the new ones
          clearAnnotations();

          if (annotationsToLoad.length > 0) {
            console.log(
              `Loading ${annotationsToLoad.length} annotations for pointer: ${currentPointerId}`,
            );
            loadAnnotations(annotationsToLoad);
          } else {
            console.log(
              `No annotations to load for pointer: ${currentPointerId}`,
            );
          }

          // Update the last pointer ID after successful load
          lastPointerIdRef.current = currentPointerId;
        }, 100);

        return () => clearTimeout(timer);
      } else {
        // If no pointer ID, just clear annotations
        clearAnnotations();
        lastPointerIdRef.current = '';
      }
    }
  }, [
    currentPointerId,
    isLoading,
    getAnnotationsToLoad,
    clearAnnotations,
    loadAnnotations,
  ]);

  // Load model when modelPath changes
  useEffect(() => {
    if (modelPath && sceneRef.current) {
      loadModel(modelPath);
    }
  }, [modelPath]);

  // Update when selectedPartName changes
  useEffect(() => {
    if (selectedPartName) {
      highlightComponent(selectedPartName);
    } else {
      resetHighlight();
    }
  }, [selectedPartName, highlightComponent, resetHighlight]);

  // Add lights to the scene
  const addLights = () => {
    if (!sceneRef.current) return;

    // Ambient light
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.4);
    sceneRef.current.add(ambientLight);

    // Hemisphere light
    const hemiLight = new THREE.HemisphereLight(0xffffff, 0x444444, 0.8);
    hemiLight.position.set(0, 1, 0);
    sceneRef.current.add(hemiLight);

    // Directional lights for shadows
    const dirLight1 = new THREE.DirectionalLight(0xffffff, 3);
    dirLight1.position.set(5, 10, 7);
    dirLight1.castShadow = true;
    sceneRef.current.add(dirLight1);

    const dirLight2 = new THREE.DirectionalLight(0xffffff, 3);
    dirLight2.position.set(-5, 5, -7);
    sceneRef.current.add(dirLight2);

    // Point light
    const pointLight = new THREE.PointLight(0xffffff, 0.5);
    pointLight.position.set(0, 10, 0);
    sceneRef.current.add(pointLight);
  };

  // Add grid to the scene
  const addGrid = () => {
    if (!sceneRef.current) return;

    const gridHelper = new THREE.GridHelper(20, 20, 0x555555, 0x333333);
    gridHelper.position.y = -0.01; // Slightly below to avoid z-fighting
    sceneRef.current.add(gridHelper);
  };

  // Load 3D model
  const loadModel = (path: string) => {
    if (!sceneRef.current || !cameraRef.current) return;

    // Reset state
    setIsLoading(true);
    setError(null);
    resetComponents();
    clearAnnotations();

    // Remove previous model if exists
    const previousModel = sceneRef.current.getObjectByName('loadedModel');
    if (previousModel) {
      sceneRef.current.remove(previousModel);
    }

    // Load new model
    const loader = new GLTFLoader();

    loader.load(
      path,
      (gltf) => {
        // Name the model for later reference
        gltf.scene.name = 'loadedModel';

        // Add to scene
        sceneRef.current?.add(gltf.scene);

        // Process components
        processComponent(gltf.scene);

        // Center and position the model
        const box = new THREE.Box3().setFromObject(gltf.scene);
        const center = box.getCenter(new THREE.Vector3());
        gltf.scene.position.sub(center);

        // Adjust camera to fit model
        const size = box.getSize(new THREE.Vector3());
        const maxDim = Math.max(size.x, size.y, size.z);
        const fov = cameraRef.current!.fov * (Math.PI / 180);
        const cameraZ = Math.abs(maxDim / 2 / Math.tan(fov / 2)) * 1.5;

        cameraRef.current!.position.set(0, size.y * 0.5, cameraZ);
        cameraRef.current!.lookAt(0, 0, 0);

        // Apply initial zoom
        cameraRef.current!.position.multiplyScalar(1 / initialZoom);

        // Update controls target
        if (controlsRef.current) {
          controlsRef.current.target.set(0, 0, 0);
          controlsRef.current.update();
        }

        // Set loading state
        setIsLoading(false);
        console.log('Model loaded successfully, ready for annotations');

        //load current pointer annotations
        if (currentPointerId) {
          const annotationsToLoad = getAnnotationsToLoad();
          if (annotationsToLoad.length > 0) {
            console.log(
              `Loading initial annotations for pointer: ${currentPointerId}`,
            );
            // Small delay to ensure scene is ready
            setTimeout(() => {
              loadAnnotations(annotationsToLoad);
              lastPointerIdRef.current = currentPointerId;
            }, 100);
          } else {
            lastPointerIdRef.current = currentPointerId;
          }
        } else {
          console.log('No current pointer, model loaded without annotations');
          lastPointerIdRef.current = '';
        }

        // Call onModelLoaded callback if provided
        if (onModelLoaded) {
          onModelLoaded();
        }
      },
      (xhr) => {
        // Progress update (could add a progress indicator here)
        console.log((xhr.loaded / xhr.total) * 100 + '% loaded');
      },
      (error: unknown) => {
        console.error('Error loading model:', error);
        setIsLoading(false);
        setError(
          'Failed to load 3D model. Please check the file path and try again.',
        );

        if (onError) {
          onError(error instanceof Error ? error : new Error(String(error)));
        }
      },
    );
  };

  // Dispose of Three.js resources
  const disposeScene = (scene: THREE.Scene) => {
    scene.traverse((object) => {
      if (object instanceof THREE.Mesh) {
        if (object.geometry) {
          object.geometry.dispose();
        }

        if (object.material) {
          if (Array.isArray(object.material)) {
            object.material.forEach((material) => material.dispose());
          } else {
            object.material.dispose();
          }
        }
      }
    });
  };

  // Extract shape from selected tool
  const getShapeFromTool = (): string => {
    if (!selectedTool) return lastShapeRef.current;

    if (selectedTool.subTool?.includes('shape-')) {
      const shape = selectedTool.subTool.replace('shape-', '');
      lastShapeRef.current = shape;
      return shape;
    }

    // For pen tool, use the last selected shape
    if (selectedTool.mainTool === 'pen') {
      return lastShapeRef.current;
    }

    return lastShapeRef.current;
  };

  // Make an annotation editable with given text
  const makeAnnotationEditable = (annotation: Annotation) => {
    if (!annotation.element) return;

    setSelectedAnnotation(annotation);

    const annotationDiv = annotation.element.querySelector(
      'div:first-child',
    ) as HTMLDivElement;
    if (!annotationDiv) return;

    // Make the div contenteditable
    annotationDiv.setAttribute('contenteditable', 'true');
    annotationDiv.focus();

    // Select all text
    const range = document.createRange();
    range.selectNodeContents(annotationDiv);
    const selection = window.getSelection();
    if (selection) {
      selection.removeAllRanges();
      selection.addRange(range);
    }

    // Handle finishing edit
    const handleBlur = () => {
      annotationDiv.removeAttribute('contenteditable');

      // Update annotation text
      updateAnnotation(annotation.id, {
        text: annotationDiv.textContent || '',
      });

      // Remove event listeners
      annotationDiv.removeEventListener('blur', handleBlur);
      annotationDiv.removeEventListener('keydown', handleKeyDown);
    };

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        annotationDiv.blur();
      }
    };

    // Add event listeners
    annotationDiv.addEventListener('blur', handleBlur);
    annotationDiv.addEventListener('keydown', handleKeyDown);
  };

  // Handle click on the model
  const handleModelClick = useCallback(
    (event: React.MouseEvent) => {
      // Check if we have a tool selected
      if (!selectedTool) return;

      // Different behavior depending on the tool
      switch (selectedTool.mainTool) {
        case 'pen':
          // Handle annotation drag if clicking an existing annotation
          if (
            event.target &&
            (event.target as HTMLElement).closest('.annotation-container')
          ) {
            // We clicked on an annotation, handle it separately
            // (All dragging is set up when annotation is created, no need to do anything here)
            return;
          }

          // If not clicking annotation, proceed with normal part selection
          const intersection = performRaycasting(event);
          if (intersection) {
            const { mesh, position } = intersection;

            // Select the component
            selectComponent(mesh, componentsRef.current.indexOf(mesh));

            // Create annotation with appropriate shape
            createAnnotation(
              mesh.userData.uniqueName, // Use part name as text
              position,
              getShapeFromTool(),
              style ?? undefined,
            );

            // Call external handler if provided
            if (onPartSelect) {
              onPartSelect(mesh.userData.uniqueName, position, event);
            }
          }
          break;

        case 'text':
          // For text tool, find and edit an existing annotation
          const closestAnnotation = findClosestAnnotation(event);
          if (closestAnnotation) {
            makeAnnotationEditable(closestAnnotation);
          }
          break;

        case 'move':
          // If we're in move-pan mode, enable panning
          if (selectedTool.subTool === 'move-pan' && controlsRef.current) {
            controlsRef.current.enablePan = true;
            controlsRef.current.enableRotate = false;
          } else if (
            selectedTool.subTool === 'move-zoom' &&
            controlsRef.current &&
            cameraRef.current
          ) {
            // Handle zoom with move-zoom mode
            // Get container center
            const rect = containerRef.current?.getBoundingClientRect();
            if (!rect) return;

            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            const clickX = event.clientX - rect.left;
            const clickY = event.clientY - rect.top;

            // Zoom in or out depending on where the click happened
            const distanceFromCenter = Math.sqrt(
              Math.pow(clickX - centerX, 2) + Math.pow(clickY - centerY, 2),
            );

            const zoomThreshold = Math.min(rect.width, rect.height) / 4;

            if (distanceFromCenter < zoomThreshold) {
              // Zoom in
              cameraRef.current.position.multiplyScalar(0.9);
            } else {
              // Zoom out
              cameraRef.current.position.multiplyScalar(1.1);
            }

            controlsRef.current.update();
          }
          break;
      }
    },
    [
      selectedTool,
      style,
      onPartSelect,
      performRaycasting,
      selectComponent,
      createAnnotation,
      componentsRef,
      findClosestAnnotation,
      makeAnnotationDraggable,
    ],
  );

  // Get container class based on selected tool
  const getContainerClass = () => {
    if (!selectedTool) return '';

    switch (selectedTool.mainTool) {
      case 'pen':
        return styles.containerPen;
      case 'move':
        if (selectedTool.subTool === 'move-pan') {
          return styles.containerMove;
        } else if (selectedTool.subTool === 'move-zoom') {
          return styles.containerZoom;
        }
        return '';
      case 'shape':
        return styles.containerShape;
      case 'text':
        return styles.containerText;
      default:
        return '';
    }
  };

  // Add state for context menu
  const [contextMenu, setContextMenu] = useState<{
    isOpen: boolean;
    position: { x: number; y: number };
    partName: string;
  }>({
    isOpen: false,
    position: { x: 0, y: 0 },
    partName: '',
  });

  // Add state for hidden parts
  const [hiddenParts, setHiddenParts] = useState<Set<string>>(new Set());

  // Add state for highlighted parts
  const [highlightedParts, setHighlightedParts] = useState<Set<string>>(
    new Set(),
  );

  // Function to handle part visibility
  const togglePartVisibility = useCallback(
    (partName: string, hide: boolean) => {
      console.log(`togglePartVisibility called for ${partName}, hide: ${hide}`);
      console.log('Current hidden parts:', Array.from(hiddenParts));
      console.log('Current highlighted parts:', Array.from(highlightedParts));

      if (!sceneRef.current) {
        console.log('No scene reference available');
        return;
      }

      const part = sceneRef.current.getObjectByName(partName);
      console.log('Found part:', part ? 'yes' : 'no');

      if (part instanceof THREE.Mesh) {
        // Simply toggle visibility without any material changes
        part.visible = !hide;
        console.log(`Set visibility to ${!hide} for ${partName}`);

        // Update hidden parts state
        setHiddenParts((prev) => {
          const next = new Set(prev);
          if (hide) {
            next.add(partName);
          } else {
            next.delete(partName);
          }
          console.log('Updated hidden parts:', Array.from(next));
          return next;
        });

        // If we're showing the part and it's highlighted, reapply highlight
        if (!hide && highlightedParts.has(partName)) {
          console.log(`Reapplying highlight for ${partName}`);
          const highlightMaterial = new THREE.MeshStandardMaterial({
            color: 0xffff00,
            emissive: 0xffff00,
            emissiveIntensity: 0.2,
            transparent: true,
            opacity: 0.8,
          });
          part.material = highlightMaterial;
        }
      } else {
        console.log('Part is not a THREE.Mesh instance');
      }
    },
    [hiddenParts, highlightedParts],
  );

  // Function to handle part highlighting
  const togglePartHighlight = useCallback(
    (partName: string) => {
      console.log(`togglePartHighlight called for ${partName}`);
      console.log('Current hidden parts:', Array.from(hiddenParts));
      console.log('Current highlighted parts:', Array.from(highlightedParts));

      if (!sceneRef.current) {
        console.log('No scene reference available');
        return;
      }

      const part = sceneRef.current.getObjectByName(partName);
      console.log('Found part:', part ? 'yes' : 'no');

      if (part instanceof THREE.Mesh) {
        const isCurrentlyHighlighted = highlightedParts.has(partName);
        const isCurrentlyHidden = hiddenParts.has(partName);
        console.log(
          `Current state - Highlighted: ${isCurrentlyHighlighted}, Hidden: ${isCurrentlyHidden}`,
        );

        // Store original material if not already stored
        if (!part.userData.originalMaterial) {
          console.log('Storing original material');
          part.userData.originalMaterial = part.material.clone();
        }

        if (isCurrentlyHighlighted) {
          console.log('Removing highlight');
          // Remove highlight
          setHighlightedParts((prev) => {
            const next = new Set(prev);
            next.delete(partName);
            return next;
          });

          // Only restore original material if the part is visible
          if (!isCurrentlyHidden && part.userData.originalMaterial) {
            console.log('Restoring original material');
            part.material = part.userData.originalMaterial;
          }
        } else {
          console.log('Adding highlight');
          // Add highlight
          setHighlightedParts((prev) => new Set(prev).add(partName));

          // Only apply highlight material if the part is visible
          if (!isCurrentlyHidden) {
            console.log('Applying highlight material');
            const highlightMaterial = new THREE.MeshStandardMaterial({
              color: 0xffff00,
              emissive: 0xffff00,
              emissiveIntensity: 0.2,
              transparent: true,
              opacity: 0.8,
            });
            part.material = highlightMaterial;
          }
        }
      } else {
        console.log('Part is not a THREE.Mesh instance');
      }
    },
    [hiddenParts, highlightedParts],
  );

  // Update restoreAllParts to also clear highlights
  const restoreAllParts = useCallback(() => {
    if (!sceneRef.current) return;

    // Clear isolated part state
    setIsolatedPartName(null);

    sceneRef.current.traverse((object) => {
      if (object instanceof THREE.Mesh && object.userData.uniqueName) {
        object.visible = true;
        // Restore original material if part was highlighted
        if (object.userData.originalMaterial) {
          object.material = object.userData.originalMaterial;
        }
      }
    });

    setHiddenParts(new Set());
    setHighlightedParts(new Set());
  }, []);

  // Function to isolate a part
  const isolatePart = useCallback(
    (partName: string) => {
      if (!sceneRef.current) return;

      // If the clicked part is already isolated, restore all parts
      if (isolatedPartName === partName) {
        restoreAllParts();
        return;
      }

      // Set isolated part state
      setIsolatedPartName(partName);

      // First, hide all parts except the one to isolate
      const partsToHide: Set<string> = new Set();
      sceneRef.current.traverse((object) => {
        if (object instanceof THREE.Mesh && object.userData.uniqueName) {
          const currentPartName = object.userData.uniqueName;
          if (currentPartName !== partName) {
            object.visible = false;
            partsToHide.add(currentPartName);
          }
        }
      });

      // Show the selected part (ensure it's not hidden)
      const partToShow = sceneRef.current.getObjectByName(partName);
      if (partToShow instanceof THREE.Mesh) {
        partToShow.visible = true;
      }

      // Update hidden parts state based on isolation
      setHiddenParts(partsToHide);
    },
    [isolatedPartName, restoreAllParts],
  );

  // Function to handle deleting a part
  const handleDeletePart = useCallback(
    (partName: string) => {
      if (!sceneRef.current) return;

      const part = sceneRef.current.getObjectByName(partName);
      if (part) {
        // Dispose of geometry and material to free up memory
        if (part instanceof THREE.Mesh) {
          if (part.geometry) part.geometry.dispose();
          if (part.material) {
            if (Array.isArray(part.material)) {
              part.material.forEach((material) => material.dispose());
            } else {
              (part.material as THREE.Material).dispose();
            }
          }
        }
        sceneRef.current.remove(part);

        // Remove from componentsRef and hiddenParts if present
        componentsRef.current = componentsRef.current.filter(
          (mesh) => mesh.userData.uniqueName !== partName,
        );
        setHiddenParts((prev) => {
          const next = new Set(prev);
          next.delete(partName);
          return next;
        });
        // Also handle if it was the highlighted or isolated part
        if (isolatedPartName === partName) setIsolatedPartName(null);
      }
    },
    [componentsRef, isolatedPartName, setIsolatedPartName],
  );

  // Add right-click handler
  const handleContextMenu = useCallback(
    (event: React.MouseEvent) => {
      event.preventDefault();

      // Only show context menu when move tool is selected
      if (!selectedTool || selectedTool.mainTool !== 'move') return;

      const intersection = performRaycasting(event);
      if (intersection) {
        const { mesh } = intersection;
        const partName = mesh.userData.uniqueName;

        setContextMenu({
          isOpen: true,
          position: { x: event.clientX, y: event.clientY },
          partName,
        });
      } else {
        // Optional: Close context menu if right-clicked on background
        setContextMenu((prev) => ({ ...prev, isOpen: false }));
      }
    },
    [selectedTool, performRaycasting],
  );

  // Add click handler to close context menu
  useEffect(() => {
    const handleClick = (event: MouseEvent) => {
      // Check if the click happened outside the context menu itself
      const contextMenuElement = document.getElementById('part-context-menu'); // Need to add an ID to the menu div
      if (
        contextMenu.isOpen &&
        contextMenuElement &&
        !contextMenuElement.contains(event.target as Node)
      ) {
        setContextMenu((prev) => ({ ...prev, isOpen: false }));
      }
      // If the menu was closed by clicking one of its items, the click handler on the button already called onClose()
    };

    // Use a slight delay or check event phase if needed to avoid closing immediately after opening
    // For now, simple document click should work if stopPropagation is used on the menu div
    document.addEventListener('click', handleClick);
    return () => document.removeEventListener('click', handleClick);
  }, [contextMenu.isOpen]); // Depend on contextMenu.isOpen to re-attach/remove listener correctly

  // Add keyboard shortcut to restore all parts (Esc key)
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        restoreAllParts();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [restoreAllParts]);

  return (
    <div
      ref={containerRef}
      className={`${styles.container} ${getContainerClass()} ${className}`}
      onClick={handleModelClick}
      onContextMenu={handleContextMenu}
    >
      {isLoading && <div className={styles.loading}>Loading 3D model...</div>}
      {error && <div className={styles.error}>{error}</div>}

      <PartContextMenu
        isOpen={contextMenu.isOpen}
        onClose={() => setContextMenu((prev) => ({ ...prev, isOpen: false }))}
        position={contextMenu.position}
        onHighlightPart={() => togglePartHighlight(contextMenu.partName)}
        partName={contextMenu.partName}
        onHidePart={() =>
          togglePartVisibility(
            contextMenu.partName,
            !hiddenParts.has(contextMenu.partName),
          )
        }
        onIsolatePart={isolatePart}
        onDeletePart={handleDeletePart}
        onShowAllParts={restoreAllParts}
        isHidden={hiddenParts.has(contextMenu.partName)}
        isIsolated={isolatedPartName === contextMenu.partName}
        isHighlighted={highlightedParts.has(contextMenu.partName)}
      />
    </div>
  );
};

export default ModelViewerCore;
