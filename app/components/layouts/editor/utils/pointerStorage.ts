// utils/pointerStorage.ts

import { SavedAnnotation } from '../ModelViewerCore/types';
import { InteractiveModePart, InteractiveModeState } from '../types';

const STORAGE_KEY = 'pointer_annotations';
const INTERACTIVE_MODE_STORAGE_KEY = 'pointer_interactive_mode';

export interface StoredPointerData {
  pointerId: string;
  annotations: SavedAnnotation[];
  timestamp: number;
}

export class PointerStorage {
  // Save annotations for a specific pointer
  static savePointerAnnotations(pointerId: string, annotations: SavedAnnotation[]): void {
    try {
      const data: StoredPointerData = {
        pointerId,
        annotations,
        timestamp: Date.now()
      };

      localStorage.setItem(`${STORAGE_KEY}_${pointerId}`, JSON.stringify(data));
      console.log(`Saved ${annotations.length} annotations for pointer ${pointerId} to localStorage`);
    } catch (error) {
      console.error('Failed to save pointer annotations to localStorage:', error);
    }
  }


  // Clear annotations for a specific pointer
  static clearPointerAnnotations(pointerId: string): void {
    try {
      localStorage.removeItem(`${STORAGE_KEY}_${pointerId}`);
      console.log(`Cleared stored annotations for pointer ${pointerId}`);
    } catch (error) {
      console.error('Failed to clear pointer annotations from localStorage:', error);
    }
  }

  // Get all stored pointer IDs
  static getAllStoredPointers(): string[] {
    const pointerIds: string[] = [];
    try {
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(STORAGE_KEY)) {
          const pointerId = key.replace(`${STORAGE_KEY}_`, '');
          pointerIds.push(pointerId);
        }
      }
    } catch (error) {
      console.error('Failed to get stored pointers from localStorage:', error);
    }
    return pointerIds;
  }

  // Clean up old stored annotations (older than 24 hours)
  static cleanupOldAnnotations(): void {
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours
    const now = Date.now();

    try {
      for (let i = localStorage.length - 1; i >= 0; i--) {
        const key = localStorage.key(i);
        if (key && key.startsWith(STORAGE_KEY)) {
          const storedData = localStorage.getItem(key);
          if (storedData) {
            const data: StoredPointerData = JSON.parse(storedData);
            if (now - data.timestamp > maxAge) {
              localStorage.removeItem(key);
              console.log(`Cleaned up old annotations for key: ${key}`);
            }
          }
        }
      }
    } catch (error) {
      console.error('Failed to cleanup old annotations:', error);
    }
  }

  // Interactive Mode Storage Functions

  // Save interactive mode for a specific pointer
  static saveInteractiveMode(pointerId: string, interactiveMode: InteractiveModePart[]): void {
    try {
      const data: InteractiveModeState = {
        pointerId,
        parts: interactiveMode,
        timestamp: Date.now()
      };

      localStorage.setItem(`${INTERACTIVE_MODE_STORAGE_KEY}_${pointerId}`, JSON.stringify(data));
      console.log(`Saved interactive mode for pointer ${pointerId}:`, interactiveMode);
    } catch (error) {
      console.error('Failed to save interactive mode to localStorage:', error);
    }
  }

  // Load interactive mode for a specific pointer
  static loadInteractiveMode(pointerId: string): InteractiveModePart[] | null {
    try {
      const storedData = localStorage.getItem(`${INTERACTIVE_MODE_STORAGE_KEY}_${pointerId}`);
      if (storedData) {
        const data: InteractiveModeState = JSON.parse(storedData);
        console.log(`Loaded interactive mode for pointer ${pointerId}:`, data.parts);
        return data.parts;
      }
    } catch (error) {
      console.error('Failed to load interactive mode from localStorage:', error);
    }
    return null;
  }

  // Clear interactive mode for a specific pointer
  static clearInteractiveMode(pointerId: string): void {
    try {
      localStorage.removeItem(`${INTERACTIVE_MODE_STORAGE_KEY}_${pointerId}`);
      console.log(`Cleared interactive mode for pointer ${pointerId}`);
    } catch (error) {
      console.error('Failed to clear interactive mode from localStorage:', error);
    }
  }

  // Get all stored interactive mode pointer IDs
  static getAllInteractiveModePointers(): string[] {
    const pointerIds: string[] = [];
    try {
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(INTERACTIVE_MODE_STORAGE_KEY)) {
          const pointerId = key.replace(`${INTERACTIVE_MODE_STORAGE_KEY}_`, '');
          pointerIds.push(pointerId);
        }
      }
    } catch (error) {
      console.error('Failed to get stored interactive mode pointers from localStorage:', error);
    }
    return pointerIds;
  }

  // Clean up old interactive mode data (older than 24 hours)
  static cleanupOldInteractiveModes(): void {
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours
    const now = Date.now();

    try {
      for (let i = localStorage.length - 1; i >= 0; i--) {
        const key = localStorage.key(i);
        if (key && key.startsWith(INTERACTIVE_MODE_STORAGE_KEY)) {
          const storedData = localStorage.getItem(key);
          if (storedData) {
            const data: InteractiveModeState = JSON.parse(storedData);
            if (now - data.timestamp > maxAge) {
              localStorage.removeItem(key);
              console.log(`Cleaned up old interactive mode for key: ${key}`);
            }
          }
        }
      }
    } catch (error) {
      console.error('Failed to cleanup old interactive modes:', error);
    }
  }
}