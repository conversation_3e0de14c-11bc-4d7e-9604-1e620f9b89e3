'use client';

import React, { useState, useEffect, useRef } from 'react';
import { ChevronDown, ChevronRight, Search, X } from 'lucide-react';
import { Input } from '@/app/components/ui/Input';
import { cn } from '@/lib/utils';
import { EditorPanelProps } from './types';

export const EditorPanel: React.FC<EditorPanelProps> = ({
  title,
  children,
  enableSearch = false,
  defaultCollapsed = false,
  onSearch,
  className,
  headerRightContent,
  showTitle = true,
}) => {
  const [isCollapsed, setIsCollapsed] = useState(defaultCollapsed);
  const [searchTerm, setSearchTerm] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const [searchVisible, setSearchVisible] = useState(false);
  const searchInputRef = useRef<HTMLDivElement>(null);
  const headerRef = useRef<HTMLDivElement>(null);

  // Handle search term changes
  useEffect(() => {
    if (onSearch) {
      onSearch(searchTerm);
    }
  }, [searchTerm, onSearch]);

  // Handle search animation
  useEffect(() => {
    if (showSearch) {
      // First set visibility to trigger the animation
      setSearchVisible(true);
      // Focus the input after animation completes
      const timer = setTimeout(() => {
        if (searchInputRef.current?.querySelector('input')) {
          (
            searchInputRef.current.querySelector('input') as HTMLInputElement
          ).focus();
        }
      }, 300); // Match the transition duration
      return () => clearTimeout(timer);
    } else {
      // When hiding, wait for animation to complete before removing from DOM
      const timer = setTimeout(() => {
        setSearchVisible(false);
      }, 300); // Match the transition duration
      return () => clearTimeout(timer);
    }
  }, [showSearch]);

  const handleCloseSearch = () => {
    setShowSearch(false);
    setSearchTerm('');
  };

  return (
    <div className={cn('flex flex-col', className)}>
      <div
        className={cn(
          'flex items-center justify-between px-2 relative overflow-hidden',
          showTitle ? 'h-10 py-2' : 'h-auto py-0',
        )}
      >
        {/* Normal header content */}
        <div
          ref={headerRef}
          className={cn(
            'absolute inset-0 px-2 py-2 flex items-center justify-between w-full transition-all duration-300 ease-in-out',
            showSearch
              ? 'translate-x-[-100%] opacity-0'
              : 'translate-x-0 opacity-100',
          )}
        >
          <div className='flex items-center'>
            {showTitle && (
              <button
                className='p-1 mr-2 rounded-md hover:bg-[#313133] text-white/70 hover:text-white'
                onClick={() => setIsCollapsed(!isCollapsed)}
              >
                {isCollapsed ? (
                  <ChevronRight size={14} />
                ) : (
                  <ChevronDown size={14} />
                )}
              </button>
            )}
            {showTitle && (
              <h2 className='text-md font-medium text-white/70'>{title}</h2>
            )}
          </div>
          <div className='flex items-center space-x-2'>
            {enableSearch && (
              <button
                className='p-1 rounded-md cursor-pointer text-white/70 hover:text-white'
                onClick={() => setShowSearch(true)}
                title='Search'
              >
                <Search size={14} />
              </button>
            )}
            {headerRightContent}
          </div>
        </div>

        {/* Search input */}
        {searchVisible && (
          <div
            ref={searchInputRef}
            className={cn(
              'absolute inset-0 px-2 py-2 flex items-center w-full transition-all duration-300 ease-in-out',
              showSearch
                ? 'translate-x-0 opacity-100'
                : 'translate-x-[100%] opacity-0',
            )}
          >
            <Input
              type='text'
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder={`Search ${title.toLowerCase()}...`}
              className='text-sm'
            />
            <button
              className='ml-2 p-1 rounded-md hover:bg-[#313133] cursor-pointer text-white/70 hover:text-white'
              onClick={handleCloseSearch}
            >
              <X size={14} />
            </button>
          </div>
        )}
      </div>

      {!isCollapsed && <div className='overflow-y-auto flex-1'>{children}</div>}
    </div>
  );
};

export default EditorPanel;
