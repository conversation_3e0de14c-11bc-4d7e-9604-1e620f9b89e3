import { Button } from '@/app/components/ui/Button';
import { CoralCircleicon } from '@/app/components/CoralCirclIcon';
import type React from 'react';

interface PlanCardProps {
  planName: string;
  price: number;
  description: string;
  isCurrentPlan?: boolean;
  addOnName?: string;
  addOnDescription?: string;
  onAddToPlan?: () => void;
}

export function PlanCard({
  planName,
  price,
  description,
  isCurrentPlan = false,
  addOnName,
  addOnDescription,
  onAddToPlan,
}: PlanCardProps) {
  return (
    <div>
      {isCurrentPlan && (
        <div className='rounded-tl-lg max-w-[7rem] text-center ml-10 p-1 rounded-tr-lg bg-[#101B14] text-[#00B453]'>
          Current Plan
        </div>
      )}
      <div className={`mb-16 bg-[#161617] rounded-xl p-6 border`}>
        <div className='flex flex-col md:flex-row items-start md:items-center gap-6 justify-between'>
          <div>
            <div className='flex items-center gap-2 mb-1'>
              <h3 className='text-xl font-normal'>{planName}</h3>
              <span className='text-muted-foreground'>
                - ${price}
                <span className='text-xs text-white/59'>/month</span>
              </span>
            </div>
            <p className='text-muted-foreground text-base text-white/70 mt-4'>
              {description}
            </p>
          </div>

          <div className='flex items-center gap-4'>
            {addOnName && addOnDescription && (
              <div className='bg-[#0D0D0D] w-full p-4 rounded-lg border border-border'>
                <div className='flex items-center gap-10'>
                  <div className='max-w-md flex items-center gap-6'>
                    <div className='flex flex-col gap-3 max-w-[14rem]'>
                      <div className='flex items-center gap-2'>
                        <div className='w-8 h-8 rounded-full bg-gradient-to-b from-[#AA423A] to-[#441A17] flex items-center justify-center'>
                          <div className='w-7 h-7 rounded-full bg-black flex items-center justify-center relative'>
                            <CoralCircleicon className='w-6 h-6' />
                          </div>
                        </div>
                        <h4 className='font-medium'>{addOnName}</h4>
                      </div>
                      <p className='text-xs text-muted-foreground text-white/70'>
                        {addOnDescription}
                      </p>
                    </div>
                  </div>
                  {onAddToPlan && (
                    <Button
                      variant='default'
                      size='default'
                      className='bg-[#AA423A] hover:bg-[#AA423A]'
                      onClick={onAddToPlan}
                    >
                      Add to plan
                    </Button>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
