import React from 'react';
import { cn } from '@/lib/utils';
import { Info } from 'lucide-react';
import { Input, InputProps } from '../ui/Input';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/app/components/ui/tooltip';

interface InfoInputProps extends Omit<InputProps, 'endAdornment'> {
  infoText: string;
  tooltipSide?: 'top' | 'right' | 'bottom' | 'left';
  infoIcon?: React.ReactNode;
  tooltipClassName?: string;
}

const InfoInput: React.FC<InfoInputProps> = ({
  infoText,
  tooltipSide = 'top',
  infoIcon,
  tooltipClassName,
  className,
  ...props
}) => {
  // Custom info icon with tooltip
  const InfoIconWithTooltip = () => (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className='cursor-help p-1 rounded-full hover:bg-[#313133]/50 transition-colors'>
            {infoIcon || <Info size={16} className='text-white/70' />}
          </div>
        </TooltipTrigger>
        <TooltipContent
          side={tooltipSide}
          className={cn(
            'border-0 bg-[#161617] text-white max-w-xs text-xs p-2 shadow-lg',
            tooltipClassName,
          )}
        >
          {infoText}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );

  return (
    <Input
      className={cn('pr-10', className)}
      endAdornment={<InfoIconWithTooltip />}
      {...props}
    />
  );
};

export { InfoInput };
