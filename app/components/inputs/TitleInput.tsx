import React, { forwardRef } from 'react';

interface TitleInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  inputRef?: React.RefObject<HTMLInputElement>;
}

const TitleInput = forwardRef<HTMLInputElement, TitleInputProps>(
  ({ value, onChange, placeholder = 'Enter title...', inputRef }, ref) => {
    return (
      <div className='mb-2'>
        <input
          type='text'
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          className='text-xl font-semibold bg-transparent border-none outline-none focus:ring-0 text-white w-full'
          ref={inputRef || ref}
        />
      </div>
    );
  },
);

TitleInput.displayName = 'TitleInput';

export default TitleInput;
