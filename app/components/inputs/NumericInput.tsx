'use client';

import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Minus, Plus } from 'lucide-react';
import { Button } from '@/app/components/ui/Button';

interface NumericInputProps {
  min?: number;
  max?: number;
  step?: number;
  value?: number;
  defaultValue?: number;
  onChange?: (value: number) => void;
  className?: string;
  disabled?: boolean;
  label?: string;
}

const NumericInput: React.FC<NumericInputProps> = ({
  min = 0,
  max = Infinity,
  step = 1,
  value: propValue,
  defaultValue = 0,
  onChange,
  className,
  disabled = false,
  label,
}) => {
  const [value, setValue] = useState<number>(
    propValue !== undefined ? propValue : defaultValue,
  );

  useEffect(() => {
    if (propValue !== undefined) {
      setValue(propValue);
    }
  }, [propValue]);

  const updateValue = (newValue: number) => {
    const validValue = Math.max(min, Math.min(max, newValue));
    if (validValue !== value) {
      setValue(validValue);
      if (onChange) {
        onChange(validValue);
      }
    }
  };

  const handleIncrement = () => updateValue(value + step);
  const handleDecrement = () => updateValue(value - step);

  return (
    <div className={cn('flex flex-col', className)}>
      {label && <label className='text-sm text-white mb-2'>{label}</label>}
      <div className='flex items-center justify-between rounded-lg border border-[#313133] overflow-hidden'>
        <Button
          type='button'
          variant='ghost'
          size='icon'
          startIcon={<Minus size={14} />}
          onClick={handleDecrement}
          disabled={disabled || value <= min}
          className='text-white/70 hover:text-white hover:bg-transparent h-10'
        />

        <div className='flex-1 text-center text-white px-1'>{value}</div>

        <Button
          type='button'
          variant='ghost'
          size='icon'
          onClick={handleIncrement}
          disabled={disabled || value >= max}
          startIcon={<Plus size={14} />}
          className='text-white/70 hover:text-white hover:bg-transparent h-10'
        />
      </div>
    </div>
  );
};

export { NumericInput };
