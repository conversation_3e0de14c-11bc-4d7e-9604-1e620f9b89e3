import React from 'react';
import Image from 'next/image';
import { cn } from '@/lib/utils';

interface ProductCardProps {
  title: string;
  description: string;
  imageSrc: string;
  imageAlt?: string;
  onClick?: () => void;
  className?: string;
  icon?: React.ReactNode;
}

export const ProductCard: React.FC<ProductCardProps> = ({
  title,
  description,
  imageSrc,
  imageAlt = '',
  onClick,
  className,
  icon,
}) => {
  return (
    <div
      className={cn(
        'flex flex-col rounded-3xl overflow-hidden bg-[#313133] h-full cursor-pointer max-w-sm',
        className,
      )}
      onClick={onClick}
    >
      <div className='relative h-[280px] flex items-center justify-center p-8 overflow-hidden'>
        <Image
          src={imageSrc}
          alt={imageAlt || title}
          fill
          className='object-contain p-6 transition-transform duration-300 hover:scale-[1.1]'
          sizes='(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw'
        />
      </div>

      <div className='p-4 bg-[#161617] border-t border-[#313133] flex items-start'>
        {icon && <div className='mr-2 mt-1'>{icon}</div>}
        <div>
          <h3 className='text-white text-xl font-medium'>{title}</h3>
          <p className='text-white/60 text-sm mt-1'>{description}</p>
        </div>
      </div>
    </div>
  );
};

export default ProductCard;
