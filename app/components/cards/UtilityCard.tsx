import React from 'react';
import { cn } from '@/lib/utils';

interface PromotionCardProps {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

export const UtilityCard: React.FC<PromotionCardProps> = ({
  children,
  className,
  onClick,
}) => {
  return (
    <div
      className={cn(
        'rounded-3xl border border-[#313133] max-w-sm transition-transform duration-300 hover:scale-[1.02] cursor-pointer',
        className,
      )}
      onClick={onClick}
    >
      {children}
    </div>
  );
};

export default UtilityCard;
