import React, { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';

export interface TabItem {
  id: string;
  label: string;
  icon?: React.ReactNode;
  content: React.ReactNode;
  disabled?: boolean;
}

interface TabbedMenuProps {
  tabs: TabItem[];
  defaultTabId?: string;
  onChange?: (tabId: string) => void;
  className?: string;
  tabsClassName?: string;
  tabClassName?: string;
  activeTabClassName?: string;
  contentClassName?: string;
  variant?: 'default' | 'underline' | 'pills' | 'bordered';
  orientation?: 'horizontal' | 'vertical';
}

const TabbedMenu: React.FC<TabbedMenuProps> = ({
  tabs,
  defaultTabId,
  onChange,
  className,
  tabsClassName,
  tabClassName,
  activeTabClassName,
  contentClassName,
  variant = 'default',
  orientation = 'horizontal',
}) => {
  const [activeTab, setActiveTab] = useState<string>(
    defaultTabId || (tabs.length > 0 ? tabs[0].id : ''),
  );
  const [indicatorStyle, setIndicatorStyle] = useState({
    left: 0,
    width: 0,
    top: 0,
    height: 0,
  });
  const tabRefs = useRef<(HTMLButtonElement | null)[]>([]);
  const tabsContainerRef = useRef<HTMLDivElement>(null);

  // Update active tab when defaultTabId changes
  useEffect(() => {
    if (defaultTabId) {
      setActiveTab(defaultTabId);
    }
  }, [defaultTabId]);

  // Update the indicator position
  useEffect(() => {
    updateIndicator();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab, variant, tabs]);

  // Add window resize listener to update indicator
  useEffect(() => {
    const handleResize = () => {
      updateIndicator();
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab, variant]);

  const updateIndicator = () => {
    if (variant !== 'default' && variant !== 'underline') return;

    const activeIndex = tabs.findIndex((tab) => tab.id === activeTab);
    if (
      activeIndex === -1 ||
      !tabRefs.current[activeIndex] ||
      !tabsContainerRef.current
    )
      return;

    const tabElement = tabRefs.current[activeIndex];
    const tabsContainer = tabsContainerRef.current;

    if (orientation === 'horizontal') {
      const containerRect = tabsContainer.getBoundingClientRect();
      const tabRect = tabElement.getBoundingClientRect();

      setIndicatorStyle({
        left: tabRect.left - containerRect.left,
        width: tabRect.width,
        top: 0,
        height: 0,
      });
    } else {
      const containerRect = tabsContainer.getBoundingClientRect();
      const tabRect = tabElement.getBoundingClientRect();

      setIndicatorStyle({
        left: 0,
        width: 0,
        top: tabRect.top - containerRect.top,
        height: tabRect.height,
      });
    }
  };

  const handleTabClick = (tabId: string) => {
    setActiveTab(tabId);
    if (onChange) {
      onChange(tabId);
    }
  };

  const activeTabContent = tabs.find((tab) => tab.id === activeTab)?.content;

  // Generate the tab classes based on the variant
  const getTabClasses = (isActive: boolean, isDisabled: boolean) => {
    const baseClasses =
      'flex items-center gap-2 px-4 py-2 transition-colors focus:outline-none whitespace-nowrap cursor-pointer';

    let variantClasses = '';
    switch (variant) {
      case 'underline':
        variantClasses = cn(
          '',
          isActive
            ? 'border-[#AA423A] text-white'
            : 'border-transparent text-white/70 hover:text-white',
        );
        break;
      case 'pills':
        variantClasses = cn(
          'rounded-md',
          isActive
            ? 'bg-[#AA423A] text-white'
            : 'text-white/70 hover:text-white hover:bg-[#313133]',
        );
        break;
      case 'bordered':
        variantClasses = cn(
          'border rounded-md',
          isActive
            ? 'border-[#AA423A] bg-[#AA423A]/10 text-white'
            : 'border-[#313133] text-white/70 hover:text-white hover:border-white/30',
        );
        break;
      default:
        variantClasses = cn(
          isActive ? 'text-white' : 'text-white/70 hover:text-white',
        );
        break;
    }

    return cn(
      baseClasses,
      variantClasses,
      isDisabled && 'opacity-50 cursor-not-allowed pointer-events-none',
      tabClassName,
      isActive && activeTabClassName,
    );
  };

  return (
    <div
      className={cn(
        'flex',
        orientation === 'horizontal' ? 'flex-col' : 'flex-row',
        className,
      )}
    >
      <div
        ref={tabsContainerRef}
        className={cn(
          'relative',
          orientation === 'horizontal'
            ? 'flex flex-row border-b '
            : 'flex flex-col  pr-4 mr-4',
          tabsClassName,
        )}
      >
        {/* Animated indicator for underline and default variants */}
        {(variant === 'underline' || variant === 'default') && (
          <div
            className={cn(
              'absolute transition-all duration-300 ease-in-out bg-[#AA423A]',
              variant === 'underline'
                ? orientation === 'horizontal'
                  ? 'bottom-0 h-0.5'
                  : 'right-0 w-0.5'
                : 'hidden',
            )}
            style={
              orientation === 'horizontal'
                ? {
                    left: `${indicatorStyle.left}px`,
                    width: `${indicatorStyle.width}px`,
                  }
                : {
                    top: `${indicatorStyle.top}px`,
                    height: `${indicatorStyle.height}px`,
                  }
            }
          />
        )}

        {tabs.map((tab, index) => (
          <button
            key={tab.id}
            ref={(el) => {
              tabRefs.current[index] = el;
            }}
            className={getTabClasses(activeTab === tab.id, !!tab.disabled)}
            onClick={() => !tab.disabled && handleTabClick(tab.id)}
            disabled={tab.disabled}
            role='tab'
            aria-selected={activeTab === tab.id}
          >
            {tab.icon && <span className='flex-shrink-0'>{tab.icon}</span>}
            <span>{tab.label}</span>
          </button>
        ))}
      </div>

      <div
        className={cn(
          'mt-4',
          orientation === 'vertical' ? 'flex-1' : '',
          contentClassName,
        )}
      >
        {activeTabContent}
      </div>
    </div>
  );
};

export { TabbedMenu };
