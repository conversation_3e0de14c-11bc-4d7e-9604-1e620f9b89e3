import * as React from 'react';
import { cn } from '@/lib/utils';

interface IconProps extends React.SVGProps<SVGSVGElement> {
  component: React.FunctionComponent<React.SVGProps<SVGSVGElement>>;
  size?: number | string;
  className?: string;
  color?: string;
  alt?: string;
  style?: Record<string, any>;
}

const Icon = React.forwardRef<SVGSVGElement, IconProps>(
  (
    {
      component: Svg,
      size,
      className,
      color = 'currentColor',
      alt,
      ...svgProps
    },
    ref,
  ) => (
    <span
      role={alt ? 'img' : undefined}
      aria-label={alt}
      className={cn('inline-block shrink-0 select-none ', className)}
      style={{
        color,
        ...(size !== undefined ? { width: size, height: size } : {}),
        ...svgProps.style,
      }}
    >
      <Svg
        ref={ref}
        className='svg-icon'
        {...svgProps}
        style={{ fill: 'currentColor', stroke: 'currentColor' }}
      />
      <style>
        {`
          .svg-icon, .svg-icon * {
            fill: currentColor !important;
            stroke: currentColor !important;
          }
        `}
      </style>
    </span>
  ),
);

Icon.displayName = 'Icon';
export { Icon };
