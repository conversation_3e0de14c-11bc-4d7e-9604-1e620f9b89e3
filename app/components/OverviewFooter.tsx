import React from 'react';
import { GitBranchIcon, SquarePen } from 'lucide-react';
import { GitHubLogoIcon } from '@radix-ui/react-icons';
import { Button } from '@/app/components/ui/Button';

interface OverviewFooterProps {
  lastUpdated: Date;
  updatedBy: string;
  isManualUpdate?: boolean;
  repository: string;
  branch: string;
  onEditClick?: () => void;
}

const OverviewFooter: React.FC<OverviewFooterProps> = ({
  lastUpdated,
  repository,
  branch,
  onEditClick,
}) => {
  // Format time as "X minutes/hours/days ago"
  const getTimeAgo = (date: Date): string => {
    const now = new Date();
    const diffInMinutes = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60),
    );

    if (diffInMinutes < 1) return 'just now';
    if (diffInMinutes < 60)
      return `${diffInMinutes} minute${diffInMinutes === 1 ? '' : 's'} ago`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24)
      return `${diffInHours} hour${diffInHours === 1 ? '' : 's'} ago`;

    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays} day${diffInDays === 1 ? '' : 's'} ago`;
  };

  const handleRepositoryClick = () => {
    console.log(`Repository clicked: ${repository}`);
    // Could navigate to repository or open in new tab
    window.open(`${repository}`, '_blank');
  };

  const handleBranchClick = () => {
    console.log(`Branch clicked: ${branch}`);
    // Could show branch selection dropdown or navigate to branch page
  };

  return (
    <div className='w-full p-2 flex justify-between items-center text-sm'>
      <div className='flex items-center space-x-2'>
        <span className='text-white/70'>Last updated</span>
        <span className='text-white font-medium'>
          {getTimeAgo(lastUpdated)}
        </span>
      </div>

      <div className='flex items-center'>
        <div
          className='flex items-center mr-4 cursor-pointer hover:underline'
          onClick={handleRepositoryClick}
        >
          <GitHubLogoIcon className='h-4 w-4 mr-2 text-white/70' />
          <span>{repository}</span>
        </div>
      </div>

      <div className='flex items-center'>
        <div
          className='flex items-center mr-4 cursor-pointer hover:underline text-white/70'
          onClick={handleBranchClick}
        >
          <GitBranchIcon className='h-4 w-4 mr-2' />
          <span>branch</span>
          <span className='text-white ml-1'>{branch}</span>
        </div>
      </div>

      <div className='flex items-center'>
        <Button
          variant='ghost'
          className='text-[#AA423A] hover:text-[#AA423A]/80 hover:bg-black/10 cursor-pointer'
          onClick={onEditClick}
        >
          Edit your docs
          <SquarePen className='h-4 w-4 ml-2' />
        </Button>
      </div>
    </div>
  );
};

export default OverviewFooter;
