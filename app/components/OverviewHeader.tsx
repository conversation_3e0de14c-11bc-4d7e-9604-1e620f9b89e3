import { Globe, Copy } from 'lucide-react';
import { useState } from 'react';
import { IntroHeader } from '@/app/components/introHeader';
import Link from 'next/link';

interface OverviewProps {
  orgName: string;
  githubRepoUrl: string;
}
export default function OverviewHeader({ orgName }: OverviewProps) {
  const [, setShowCopied] = useState(false);

  const handleCopy = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setShowCopied(true);
      setTimeout(() => setShowCopied(false), 2000); // Hide after 2 seconds
    } catch (err) {
      console.error('Failed to copy text:', err);
    }
  };

  return (
    <div className='px-3 py-4'>
      <div className='flex flex-row justify-between items-start'>
        <IntroHeader
          hasDivider={false}
          title={`Good Morning, ${orgName}`}
          description='Welcome to your documentation portal'
        />
        <div className='flex flex-col items-end text-right gap-1'>
          <p className='text-sm text-white/70 w-full text-right'>
            Your docs URL
          </p>
          <div className='flex justify-end items-center w-full gap-2'>
            <p className='mr-1'>{`${orgName?.toLowerCase()}.tnkr.ai`}</p>
            <div className='inline-flex items-center justify-center text-gray-400 hover:text-white cursor-pointer'>
              <Copy
                className='h-4 w-4'
                onClick={() => handleCopy(`${orgName}.tnkr.ai`)}
              />
            </div>
          </div>
          <div className='w-full text-right'>
            <Link href='/overview/settings/domain'>
              <div className='inline-flex items-center text-[#AA423A] hover:text-red-300 cursor-pointer gap-2'>
                Add a custom domain
                <Globe className='h-4 w-4' />
              </div>
            </Link>
          </div>
        </div>
      </div>

      <div className='mt-5 mb-5 border-t border-gray-800'></div>
    </div>
  );
}
