import React from 'react';
import { CircleCheck, XCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Icon } from '../CustomIcon';

interface StatusBadgeProps {
  status: 'success' | 'pending' | 'failed';
  className?: string;
  customText?: string;
}
import LoaderSvg from '@/assets/icons/loader.svg';

export function StatusBadge({
  status,
  className,
  customText,
}: StatusBadgeProps) {
  const config = {
    success: {
      icon: <CircleCheck size={16} />,
      text: 'Success',
      bgColor: 'bg-[#101B14]',
      textColor: 'text-[#00B453]',
    },
    pending: {
      icon: <Icon component={LoaderSvg} size={16} />,
      text: 'Pending',
      bgColor: 'bg-[#DB783E33]',
      textColor: 'text-[#F5A524]',
    },
    failed: {
      icon: <XCircle size={16} />,
      text: 'Failed',
      bgColor: 'bg-red-950',
      textColor: 'text-red-500',
    },
  };

  const { icon, text, bgColor, textColor } = config[status];

  return (
    <div
      className={cn(
        'flex items-center gap-2 text-xs px-2 py-1 rounded-sm',
        bgColor,
        textColor,
        className,
      )}
    >
      {icon}
      <span>{customText || text}</span>
    </div>
  );
}
