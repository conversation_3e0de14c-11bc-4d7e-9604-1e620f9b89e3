import React from 'react';
import { CircleCheck, XCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface AuthBadgeProps {
  status: 'authorized' | 'unauthorized';
  className?: string;
}

export function AuthBadge({ status, className }: AuthBadgeProps) {
  const config = {
    authorized: {
      icon: <CircleCheck size={16} />,
      text: 'Authorised',
      bgColor: 'bg-[#101B14]',
      textColor: 'text-[#00B453]',
    },
    unauthorized: {
      icon: <XCircle size={16} />,
      text: 'Unauthorised',
      bgColor: 'bg-red-950',
      textColor: 'text-red-500',
    },
  };

  const { icon, text, bgColor, textColor } = config[status];

  return (
    <div
      className={cn(
        'flex items-center gap-2 text-xs px-2 py-1 rounded-sm',
        bgColor,
        textColor,
        className,
      )}
    >
      {icon}
      <span>{text}</span>
    </div>
  );
}
