import { cn } from '@/lib/utils';
import { Icon } from '../CustomIcon';

type StatusType = 'success' | 'pending' | 'required' | 'idle' | 'misconfigured';

import CloudSuccessSvg from '@/assets/icons/cloud_success.svg';
import LoaderSvg from '@/assets/icons/loader.svg';
import SettingsSvg from '@/assets/icons/settings.svg';

interface StatusPillProps {
  status: StatusType;
  className?: string;
  customText?: string; // New prop for custom text
}

const statusConfig = {
  success: {
    icon: <Icon component={CloudSuccessSvg} />,
    textColor: 'text-[#4ADE80]',
    borderColor: 'border border-[#313133]',
    defaultText: 'Connected',
  },
  pending: {
    icon: <Icon component={LoaderSvg} />,
    textColor: 'text-[#F5A524]',
    defaultText: 'Fetching Configurations',
    borderColor: 'border border-[#313133]',
  },
  required: {
    icon: <Icon component={SettingsSvg} />,
    textColor: 'text-white',
    defaultText: 'DNS Configurations Required',
    borderColor: 'border border-[#313133]',
  },
  idle: {
    icon: <Icon component={SettingsSvg} />,
    textColor: 'text-[#F5A524]',
    defaultText: 'Idle',
    borderColor: 'border border-[#313133]',
  },
  misconfigured: {
    icon: <Icon component={SettingsSvg} />,
    textColor: 'text-[#F43F5E]',
    defaultText: 'Misconfigured',
    borderColor: 'border border-[#313133]',
  },
};

export function StatusPill({ status, customText, className }: StatusPillProps) {
  const config = statusConfig[status];
  const Icon = config.icon;

  return (
    <div
      className={cn(
        'inline-flex items-center gap-2 px-3 py-1 rounded-lg text-sm whitespace-nowrap w-fit',
        config.borderColor,
        config.textColor,
        className,
      )}
    >
      {Icon}
      <span>{customText || config.defaultText}</span>
    </div>
  );
}
