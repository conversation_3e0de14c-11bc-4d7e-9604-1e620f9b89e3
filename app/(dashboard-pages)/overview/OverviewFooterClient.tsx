// app/HomeClient.tsx
'use client';

import OverviewFooter from '@/app/components/OverviewFooter';

interface DocMetadata {
  lastUpdated: Date;
  updatedBy: string;
  isManualUpdate: boolean;
  repository: string;
  branch: string;
}

interface HomeClientProps {
  metadata: DocMetadata | null;
}

export default function OverviewFooterClient({ metadata }: HomeClientProps) {
  const handleEditClick = () => {
    const githubRepoUrl = metadata?.repository; // Replace with your actual URL or dynamic variable
    console.log('Edit clicked, opening:', githubRepoUrl);
    window.open(githubRepoUrl, '_blank');
  };

  return (
    metadata && (
      <OverviewFooter
        lastUpdated={new Date(metadata.lastUpdated)}
        updatedBy={metadata.updatedBy}
        isManualUpdate={metadata.isManualUpdate}
        repository={metadata.repository}
        branch={metadata.branch}
        onEditClick={handleEditClick}
      />
    )
  );
}
