'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/app/components/ui/Button';
import SelectAccordion from '@/app/components/ui/SelectAccordion';
import { EmptyState } from '@/app/components/EmptyState';
import { PlusIcon, AlertCircle, Plus } from 'lucide-react';
import { UploadModal } from '@/app/components/modals/UploadModal';
import { Input } from '@/app/components/ui/Input';
import { ToggleButton } from '@/app/components/ui/ToggleButton';
import { ModalFooter } from '@/app/components/modals/Modal';
import { useToast } from '@/app/hooks/use-toast';
import ProductCard from '@/app/components/cards/ProductCard';
import CubeCogSvg from '@/assets/icons/cube_cog.svg';
import { Icon } from '@/app/components/CustomIcon';
import UtilityCard from '@/app/components/cards/UtilityCard';
import Link from 'next/link';

// Product interface
interface Product {
  id: string;
  name: string;
  description: string;
  imageSrc: string;
  createdAt: Date;
}

export default function HardwareDocsPage() {
  const router = useRouter();
  const { toast } = useToast();

  // State
  const [products, setProducts] = useState<Product[]>([]);
  const [sortOption, setSortOption] = useState({
    value: 'lastModified',
    label: 'Last Modified',
  });
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [productName, setProductName] = useState<string>('');
  const [hierarchyAdjustment, setHierarchyAdjustment] =
    useState<string>('Basic');
  const [, setSelectedFile] = useState<File | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // In a real app, you would fetch products from your API
  useEffect(() => {
    // Simulating loading products from API
    // For demo purposes, initialize with empty products array
    setProducts([]);
  }, []);

  const sortOptions = [
    { value: 'lastModified', label: 'Last Modified' },
    { value: 'name', label: 'Name' },
    { value: 'type', label: 'Type' },
  ];

  const toggleOptions = [
    { id: 'None', label: 'None' },
    { id: 'Basic', label: 'Basic' },
  ];

  const handleOpenUploadModal = () => {
    setIsUploadModalOpen(true);
  };

  const handleCloseUploadModal = () => {
    setIsUploadModalOpen(false);
    setProductName('');
    setHierarchyAdjustment('Basic');
    setSelectedFile(null);
  };

  const handleFileUpload = (file: File) => {
    setSelectedFile(file);
  };

  const handleSubmit = () => {
    if (productName.trim()) {
      setIsLoading(true);

      // Simulate API call to create a product
      setTimeout(() => {
        // Create a new product object
        const newProduct: Product = {
          id: Date.now().toString(),
          name: productName,
          description: `Created ${new Date().toLocaleDateString()}`,
          imageSrc: '/assets/img/parol6.png',
          createdAt: new Date(),
        };

        // Add the new product to the products array
        setProducts([...products, newProduct]);

        // Show success toast
        toast({
          title: 'Product created',
          description: `${productName} has been created successfully.`,
          variant: 'default',
        });

        // Close modal and reset form
        handleCloseUploadModal();
        setIsLoading(false);
      }, 1500); // Simulate processing time
    }
  };

  const handleProductClick = (productId: string) => {
    // Navigate to the product detail page
    router.push(`/overview/editor/hardware-docs/products/${productId}`);
  };

  // Custom form elements to insert into the modal
  const productFormElements = (
    <>
      {/* Product Name Input */}
      <div className='mb-6'>
        <label className='block text-white/70 text-sm mb-2'>Product Name</label>
        <Input
          type='text'
          placeholder='Enter product name'
          value={productName}
          className='max-w-[80%]'
          onChange={(e) => setProductName(e.target.value)}
        />
      </div>

      {/* Hierarchy Adjustment */}
      <div className='mb-6'>
        <label className='block text-white/70 text-sm mb-2'>
          Hierarchy Adjustment
        </label>
        <ToggleButton
          options={toggleOptions}
          value={hierarchyAdjustment}
          onChange={setHierarchyAdjustment}
        />
        <div className='flex items-center mt-3 text-sm text-white/70'>
          <AlertCircle className='h-4 w-4 mr-2' />
          <p>
            This allows Leonardo recommend an initial hierarchy for your product
          </p>
        </div>
      </div>
    </>
  );

  // Custom footer for the modal
  const customFooter = (
    <ModalFooter
      onCancel={handleCloseUploadModal}
      onConfirm={handleSubmit}
      cancelLabel='Cancel'
      confirmLabel='Upload'
      showCancel={true}
      disabled={!productName.trim() || isLoading}
    />
  );

  // Sort products based on current sort option
  const sortedProducts = [...products].sort((a, b) => {
    switch (sortOption.value) {
      case 'name':
        return a.name.localeCompare(b.name);
      case 'type':
        // In a real app, you might have a type property to sort by
        return 0;
      case 'lastModified':
      default:
        return b.createdAt.getTime() - a.createdAt.getTime();
    }
  });

  // Determine if we should show empty state or product list
  const showEmptyState = products.length === 0;

  return (
    <div className='p-12 m-5'>
      {/* Header section with controls */}
      <div className='flex justify-between items-center w-full mb-6'>
        <div className='w-100'>
          <SelectAccordion
            variant='bare'
            options={sortOptions}
            defaultValue={sortOption.value}
            onChange={(value) =>
              setSortOption(
                sortOptions.find((option) => option.value === value)!,
              )
            }
          />
        </div>
        <div className='flex gap-3'>
          <Button
            variant='default'
            onClick={handleOpenUploadModal}
            disabled={products.length > 0} // Only allow one product for now
          >
            New Product
          </Button>
          <Button variant='destructive'>Share</Button>
        </div>
      </div>

      {/* Empty state or product grid */}
      {showEmptyState ? (
        <EmptyState
          message='Your documentation starts with a hardware product. Create a new product to begin your documentation process'
          title='No Products Yet'
          buttonText='New Product'
          buttonIcon={<PlusIcon />}
          onButtonClick={handleOpenUploadModal}
        />
      ) : (
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-4 gap-6'>
          {sortedProducts.map((product) => (
            <ProductCard
              key={product.id}
              title={product.name}
              description={product.description}
              imageSrc={product.imageSrc}
              icon={<Icon component={CubeCogSvg} color='#AA423A' />}
              onClick={() => handleProductClick(product.id)}
            />
          ))}

          {/* Upgrade card - only shown when user has products but can't add more */}
          {products.length > 0 && (
            <UtilityCard>
              <div className='px-4 py-4 relative flex flex-col gap-4 h-full'>
                <div className='flex items-center justify-center py-10'>
                  <Button
                    variant='default'
                    className='cursor-pointer w-12 h-12 rounded-full flex items-center justify-center mb-6'
                    size='icon'
                    startIcon={<Plus size={24} />}
                  />
                </div>
                <div className='flex flex-col items-start'>
                  <h3 className='text-white text-xl font-medium mb-2'>
                    Upgrade to add more products.
                  </h3>
                  <p className='text-white/60 mb-4'>
                    Get unlimited everything on the
                  </p>
                  <Link
                    href='/plans'
                    className='text-[#AA423A] hover:underline'
                  >
                    Growth Plan
                  </Link>
                </div>
              </div>
            </UtilityCard>
          )}
        </div>
      )}

      {/* Upload Modal */}
      {isUploadModalOpen && (
        <UploadModal
          isOpen={isUploadModalOpen}
          onClose={handleCloseUploadModal}
          onUpload={handleFileUpload}
          modalTitle='Product'
          footerContent={customFooter}
        >
          {productFormElements}
        </UploadModal>
      )}
    </div>
  );
}
