'use client';

import { SidebarNavigation } from '@/app/components/SideNavigation';
import { useOrganization } from '@/app/utils/useOrganization';
import { useState } from 'react';
import { cn } from '@/lib/utils';

export default function OverviewLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { organization } = useOrganization();
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  const handleToggleSidebar = () => {
    setIsSidebarCollapsed((prev) => !prev);
  };

  return (
    <div className='flex min-h-screen bg-black'>
      {/* Sidebar Navigation */}
      <SidebarNavigation
        orgName={organization?.name || ''}
        isCollapsed={isSidebarCollapsed}
        onToggleAction={handleToggleSidebar}
      />

      {/* Main Content */}
      <main
        className={cn(
          'rounded-3xl min-h-screen overflow-auto relative flex-1 mt-5  bg-[#0D0D0D]',
          isSidebarCollapsed ? 'ml-[80px]' : 'ml-[240px]', // Adjust margin dynamically
        )}
      >
        {children}
      </main>
    </div>
  );
}
