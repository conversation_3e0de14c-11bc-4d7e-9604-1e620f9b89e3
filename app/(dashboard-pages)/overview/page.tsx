'use client';

import DeployedSitePreview from '@/app/components/DeployedSitePreview';
import OverviewHeader from '@/app/components/OverviewHeader';
import ActivityHistory from '@/app/components/ActivityHistory';
import { useOrganization } from '@/app/utils/useOrganization';
import OverviewFooterClient from './OverviewFooterClient';

export default function OverviewPage() {
  const { organization, activityData, docMetadata } = useOrganization();

  return (
    <div className='p-6 m-5'>
      <OverviewHeader
        orgName={organization?.name || ''}
        githubRepoUrl={docMetadata?.repository || ''}
      />
      <DeployedSitePreview
        url={`https://${organization?.name}.tnkr.ai`}
        height={300}
        width='100%'
      />
      <OverviewFooterClient metadata={docMetadata} />
      <div className='my-12 h-px w-full bg-gray-800' />
      <ActivityHistory
        data={activityData}
        title='Activity History'
        description='Keep an eye on changes and updates made to your docs'
      />
    </div>
  );
}
