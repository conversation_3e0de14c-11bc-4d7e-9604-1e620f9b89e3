'use client';

import React, { useState } from 'react';
import {
  Check,
  Bot,
  Users,
  MessageSquare,
  Sparkles,
  User,
  Globe,
  Component,
  Search,
  NotepadText,
  Drill,
  ChartLine,
  Workflow,
  Blocks,
  SquareMinus,
  ShieldPlus,
  Fingerprint,
  Headset,
} from 'lucide-react';
import { IntroHeader } from '@/app/components/introHeader';
import { PlanCard } from '@/app/components/PlanCard';
import { CustomRadio } from '@/app/components/ui/CustomRadio';
import { PricingCard } from '@/app/components/PricingCard';
import { GiQuill } from 'react-icons/gi';
import { TbHeartPlus } from 'react-icons/tb';

const BillingPage = () => {
  const [isYearlyBilling, setIsYearlyBilling] = useState(false);

  const handleAddToPlan = () => {
    // Handle add to plan logic
    console.log('Adding to plan...');
  };

  const pricingCards = [
    {
      name: 'Hobby',
      price: 0,
      description: 'For individual hobbyists.',
      variant: 'current' as const,
      offerings: [
        { text: 'Beautiful out of the box', icon: <Sparkles size={18} /> },
        { text: 'Single User', icon: <User size={18} /> },
        { text: 'Custom Domain', icon: <Globe size={18} /> },
        { text: 'Built in components', icon: <Component size={18} /> },
        { text: 'In app search', icon: <Search size={18} /> },
        { text: 'Web Editor', icon: <NotepadText size={18} /> },
      ],
      isCurrentPlan: true,
    },
    {
      name: 'Startup',
      price: 180,
      description: 'For small teams and startups.',
      discount: '15% off',
      offerings: [
        { text: 'Everything in hobby, plus', icon: <Check size={18} /> },
        { text: 'Hardware docs components', icon: <Drill size={18} /> },
        { text: '5 users', icon: <Users size={18} /> },
        { text: 'AI workshop assistant', icon: <Bot size={18} /> },
        { text: 'AI Technical Writer', icon: <GiQuill size={18} /> },
        { text: 'Advanced Analytics', icon: <ChartLine size={18} /> },
      ],
    },
    {
      name: 'Growth',
      price: 700,
      description: 'For growing teams.',
      discount: '15% off',
      offerings: [
        { text: 'Everything in startup, plus', icon: <Check size={18} /> },
        { text: 'Integrations (Onshape, So..)', icon: <Workflow size={18} /> },
        { text: '20 users', icon: <Users size={18} /> },
        { text: 'Remove Tnkr Branding', icon: <Blocks size={18} /> },
        { text: 'Multiple Products', icon: <SquareMinus size={18} /> },
        {
          text: 'Forums and Open Discussions',
          icon: <MessageSquare size={18} />,
        },
      ],
    },
    {
      name: 'Enterprise',
      price: 'Custom',
      description: 'For large orgs needing an enterprise-grade solution',
      variant: 'contact' as const,
      offerings: [
        { text: 'Everything in growth, plus', icon: <Check size={18} /> },
        { text: 'Advanced Security', icon: <ShieldPlus size={18} /> },
        { text: 'SSO Login', icon: <Fingerprint size={18} /> },
        { text: '99.9% Uptime SLA', icon: <TbHeartPlus size={18} /> },
        { text: 'Support SLA', icon: <Headset size={18} /> },
      ],
    },
  ];

  return (
    <div className='p-12 m-5'>
      <IntroHeader
        title='Billing Manager'
        description='View and manage your organisation’s billing plans.'
      />

      <div className='mt-14'>
        <PlanCard
          planName='Hobby'
          price={0}
          description='For individual hobbyists who want to create simple surface level docs for their robots.'
          isCurrentPlan={true}
          addOnName='Leonardo'
          addOnDescription="The only technical writer you need for your robot's docs."
          onAddToPlan={handleAddToPlan}
        />
        <div className='mt-10 border border-t border-b-0 border-l-0 border-r-0 border-white/10'>
          <div className='mt-10'>
            <div className='flex justify-between items-center mb-8'>
              <IntroHeader
                title='All Plans'
                hasDivider={false}
                description='You can upgrade or change your plan here'
              />
              <div className='flex items-center gap-2'>
                <p className='text-base text-balance text-white'>
                  Save with yearly billing
                </p>
                <CustomRadio
                  checked={isYearlyBilling}
                  onChange={setIsYearlyBilling}
                  // Optional: customize colors
                  trackClassName='bg-orange-600/30'
                  knobClassName='bg-orange-500'
                />
              </div>
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
              {pricingCards.map((card, index) => (
                <PricingCard
                  key={index}
                  showDiscount={isYearlyBilling}
                  {...card}
                  onAction={() => console.log(`Action for ${card.name} plan`)}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BillingPage;
