'use client';

import React, { useState } from 'react';
import { Input } from '@/app/components/ui/Input';
import { Button } from '@/app/components/ui/Button';
import { Badge } from '@/app/components/ui/badge';
import { Download, Info, Lock, TriangleAlert } from 'lucide-react';
import {
  ModalContainer,
  ModalBody,
  ModalFooter,
} from '@/app/components/modals/Modal';
import { useOrganization } from '@/app/utils/useOrganization';
import { IntroHeader } from '@/app/components/introHeader';

const GeneralSettingsPage = () => {
  const { organization } = useOrganization();
  const [deploymentName, setDeploymentName] = useState('');
  const [deleteReason, setDeleteReason] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const handleSaveChanges = async () => {
    if (!organization) return;

    setIsLoading(true);
    try {
      // Implementation for saving changes
      // You can use your supabase client here
      setIsLoading(false);
    } catch (error) {
      console.error('Error saving changes:', error);
      setIsLoading(false);
    }
  };

  const handleDeleteClick = () => {
    setShowDeleteModal(true);
  };

  const handleConfirmDelete = async () => {
    if (!organization) return;

    try {
      // Implementation for delete organization
      // You can use your supabase client here
      setShowDeleteModal(false);
    } catch (error) {
      console.error('Error deleting organization:', error);
    }
  };

  React.useEffect(() => {
    if (organization) {
      setDeploymentName(organization.name);
    }
  }, [organization]);

  const DeleteModalIcon = () => (
    <div className='relative'>
      <div className='flex justify-center items-center bg-[#AA423A33] w-10 h-10 rounded-full'>
        <TriangleAlert className='text-[#AA423A]' />
      </div>
    </div>
  );

  return (
    <div className='p-12 m-5'>
      <IntroHeader
        title='General'
        description='General settings for your deployment'
      />

      <div className='space-y-12'>
        {/* Deployment Name Section */}
        <div className='space-y-4'>
          <h2 className='text-lg font-medium'>Deployment Name</h2>
          <p className='text-muted-foreground text-sm'>
            The name that will be displayed in the top bar. Keep it simple.
          </p>

          <div className='max-w-md'>
            <Input
              value={deploymentName}
              onChange={(e) => setDeploymentName(e.target.value)}
              className='border-[#313133]'
            />
          </div>

          <div>
            <Button
              variant='destructive'
              size='default'
              loading={isLoading}
              onClick={handleSaveChanges}
              className='cursor-pointer'
            >
              Save Changes
            </Button>
          </div>
        </div>

        {/* Export Content Section */}
        <div className='space-y-4'>
          <div className='flex items-center gap-2'>
            <h2 className='text-lg font-medium'>Export Content</h2>
            <Badge className='bg-[#DB783E33]/20 text-[#DB783E] border-[#DB783E]/30'>
              <Info className='size-3' />
              Available on Growth
            </Badge>
          </div>

          <p className='text-muted-foreground text-sm'>
            Export your entire documentation as PDF with table of content.
          </p>

          <div>
            <Button
              variant='destructive'
              size='default'
              startIcon={<Download className='size-4' />}
              className='cursor-pointer text-white'
            >
              Export all content
            </Button>
          </div>
        </div>

        {/* Delete Organisation Section */}
        <div className='space-y-4 flex flex-col gap-1'>
          <div className='flex items-center gap-2'>
            <h2 className='text-lg font-medium'>Delete My Organisation</h2>
            <Badge className='bg-[#AA423A33]/20 text-[#AA423A] border-[#AA423A]/30'>
              <TriangleAlert className='size-3' />
              Danger zone
            </Badge>
          </div>

          <p className='text-muted-foreground text-sm'>
            Your organisation will be deleted and cannot be restored. This is
            irreversible. Please be certain.
          </p>

          <div className='space-y-2'>
            <label
              htmlFor='delete-reason'
              className='text-sm font-medium block'
            >
              Please let us know why you want your account to be deleted
            </label>
            <textarea
              id='delete-reason'
              value={deleteReason}
              onChange={(e) => setDeleteReason(e.target.value)}
              rows={4}
              className='w-full mt-4 rounded-lg max-w-lg border border-[#313133] px-3 py-2 text-sm resize-none focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2'
              placeholder='Your feedback helps us improve our service...'
            />
          </div>
          <div>
            <Button
              variant='default'
              size='default'
              className='cursor-pointer'
              onClick={handleDeleteClick}
            >
              Delete {organization?.name || 'Organization'}
            </Button>
          </div>
        </div>
      </div>

      {/* Delete Organization Modal */}
      <ModalContainer
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
      >
        <ModalBody
          icon={<DeleteModalIcon />}
          title='Delete Organisation'
          description={`You are about to request the delete of your organisation ${organization?.name} from our records`}
        >
          <div className='flex items-center gap-2 py-2 px-3 rounded-md bg-red-500/10 border border-red-500/20 mt-4'>
            <Lock className='size-4 text-red-400' />
            <p className='text-xs text-red-200'>
              This action will permanently delete all your documentation,
              settings, and user data.
            </p>
          </div>
        </ModalBody>
        <ModalFooter
          onCancel={() => setShowDeleteModal(false)}
          onConfirm={handleConfirmDelete}
          confirmLabel='I understand, proceed'
          showCancel={true}
          cancelLabel='Cancel'
        />
      </ModalContainer>
    </div>
  );
};

export default GeneralSettingsPage;
