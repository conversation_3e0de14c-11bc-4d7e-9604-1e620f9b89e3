'use client';

import React, { useEffect, useState, useCallback, useRef } from 'react';
import { IntroHeader } from '@/app/components/introHeader';
import TnkrTable from '@/app/components/ui/TnkrTable';
import { LogOut, Timer, Trash } from 'lucide-react';
import { TabItem, Tabs } from '@/app/components/Tabs';
import {
  Avatar,
  AvatarImage,
  AvatarFallback,
} from '@/app/components/ui/Avatar';
import { StatusBadge } from '@/app/components/pills/StatusBadge';
import SelectAccordion from '@/app/components/ui/SelectAccordion';
import { RotateCcw } from 'lucide-react';
import { AlertTriangle, Link } from 'lucide-react';
import {
  ModalContainer,
  ModalBody,
  ModalFooter,
} from '@/app/components/modals/Modal';
import { Button } from '@/app/components/ui/Button';
import { Label } from '@/app/components/ui/Label';
import { EmailInput } from '@/app/components/EmailInput';
import { useToast } from '@/app/hooks/use-toast';
import { Icon } from '@/app/components/CustomIcon';
import { Toaster } from '@/app/components/ui/toast';
import {
  getWorkspaceMembersAction,
  getWorkspaceInviteLinkAction,
  getWorkspaceRolesAction,
  inviteWorkspaceMembersAction,
  updateMemberRoleAction,
  removeWorkspaceMemberAction,
  leaveWorkspaceAction,
} from '@/app/common/workspace-actions';
import { useOrganization } from '@/app/utils/useOrganization';
import addPeople from '@/assets/icons/add_people.svg';
interface TableData {
  user: {
    id: string; // Add this
    name: string;
    email: string;
    role: string;
    avatarUrl?: string;
  };
  status: 'success' | 'failed' | 'pending';
  statusText: string;
  isPendingInvitation?: boolean;
}

interface Roles {
  value: string;
  label: string;
  description: string;
}

export default function MembersPage() {
  const [members, setMembers] = useState<TableData[]>([]);
  const [inviteLink, setInviteLink] = useState('');
  const [roleOptions, setRoleOptions] = useState<Roles[]>([]);
  const [isLoadingLink, setIsLoadingLink] = useState(true);
  const [, setIsLoadingRoles] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const { organization } = useOrganization();
  const { toast, dismiss } = useToast();
  const [isRemovingMember, setIsRemovingMember] = useState(false);
  const [, setIsLeavingWorkspace] = useState(false);
  const inviteToastIdRef = useRef<string | null>(null);

  //fetch members
  const loadMembers = useCallback(async () => {
    setIsLoading(true);
    try {
      if (!organization) return;
      const result = await getWorkspaceMembersAction(organization.id);
      if ('error' in result) {
        toast({
          title: 'Error',
          description: result.error,
          variant: 'destructive',
        });
        return;
      }

      const formattedMembers: TableData[] = result.members.map((member) => ({
        user: {
          name: `${member.first_name} ${member.last_name}`.trim(),
          email: member.email,
          id: member.id,
          role: member.tnkr_org_user_roles[0]?.role_id,
        },
        status: member.is_active ? 'success' : 'pending',
        statusText: member.is_active ? 'Active' : 'Pending',
      }));

      const formattedInvitations: TableData[] = result.invitations.map(
        (invitation) => ({
          user: {
            name: 'Organization Member',
            email: invitation.email,
            id: invitation.id,
            role: invitation.tnkr_org_roles?.id,
          },
          status: 'pending',
          statusText: 'Pending Invitation',
          isPendingInvitation: true,
        }),
      );

      setMembers([...formattedMembers, ...formattedInvitations]);
    } catch (error) {
      console.error(error);
      toast({
        title: 'Error',
        description: 'Failed to load workspace members',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [organization, toast, setMembers, setIsLoading]);

  // Fetch invite link
  const loadInviteLink = useCallback(async () => {
    if (!organization) return;

    setIsLoadingLink(true);
    try {
      const result = await getWorkspaceInviteLinkAction(organization.id);
      if ('success' in result && result.inviteLink) {
        setInviteLink(result.inviteLink);
      } else if ('error' in result) {
        toast({
          title: 'Error',
          description: result.error,
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Failed to generate invite link:', error);
    } finally {
      setIsLoadingLink(false);
    }
  }, [organization, toast, setInviteLink, setIsLoadingLink]);

  const loadRoles = useCallback(async () => {
    if (!organization) return;
    setIsLoadingRoles(true);
    try {
      const result = await getWorkspaceRolesAction(organization.id, false);
      if ('success' in result && result.roles) {
        setRoleOptions(() =>
          result.roles.map((role) => ({
            value: role.id,
            label: role.name.charAt(0).toUpperCase() + role.name.slice(1),
            description: role.description || `${role.name} role`,
          })),
        );
      } else if ('error' in result) {
        toast({
          title: 'Error',
          description: result.error,
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Failed to fetch roles:', error);
    } finally {
      setIsLoadingRoles(false);
    }
  }, [organization, toast, setRoleOptions, setIsLoadingRoles]);

  const handleCopyInviteLink = useCallback(() => {
    navigator.clipboard.writeText(inviteLink);
    toast({
      title: 'Link copied!',
      description: 'Invite link has been copied to clipboard.',
      duration: 2000,
    });
  }, [inviteLink, toast]);

  const showInvitePeopleToast = useCallback(() => {
    const { id } = toast({
      variant: 'modal',
      title: 'Invite link to add members',
      description:
        'Only people with permission to invite members can see this.',
      icon: <Icon component={addPeople} className='w-6 h-6' />,
      persistent: true,
      confirmLabel: 'Copy link',
      confirmLabelColor: '#DB783E',
      onConfirm: handleCopyInviteLink,
      confirmIcon: <Link className='h-4 w-4 mr-2' />,
      buttonBgColor: '#DB783E33',
      buttonHoverColor: '#DB783E33',
    });
    inviteToastIdRef.current = id;
  }, [toast, handleCopyInviteLink]);

  useEffect(() => {
    loadInviteLink();
    loadMembers();
    loadRoles();
  }, [loadInviteLink, loadMembers, loadRoles]);

  useEffect(() => {
    if (!isLoadingLink && inviteLink) {
      showInvitePeopleToast();
    }
  }, [isLoadingLink, inviteLink, showInvitePeopleToast]);

  useEffect(() => {
    return () => {
      if (inviteToastIdRef.current) {
        dismiss(inviteToastIdRef.current);
      }
    };
  }, []);

  const [showRemoveModal, setShowRemoveModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<TableData | null>(null);
  const [showAddMemberModal, setShowAddMemberModal] = useState(false);
  const [emails, setEmails] = useState<string[]>([]);
  const [selectedRole, setSelectedRole] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const WarningIcon = () => (
    <div className='relative'>
      <div className='flex justify-center items-center bg-[#AA423A33] w-10 h-10 rounded-full'>
        <AlertTriangle className='text-[#AA423A]' />
      </div>
    </div>
  );

  const handleRoleChange = (row: TableData) => {
    setSelectedUser(row);
    setShowRemoveModal(true);
  };

  const handleConfirmRemove = async () => {
    if (!selectedUser || !organization) {
      setShowRemoveModal(false);
      setSelectedUser(null);
      return;
    }

    setIsRemovingMember(true);
    try {
      const formData = new FormData();
      formData.append('userId', selectedUser.user.id);
      formData.append('organizationId', organization.id);

      const result = await removeWorkspaceMemberAction(formData);

      if ('error' in result) {
        toast({
          title: 'Error',
          description: result.error,
          variant: 'destructive',
        });
      } else {
        toast({
          title: 'Success',
          description: 'Member removed successfully',
        });
        // Refresh the members list
        await loadMembers();
      }
    } catch (error) {
      console.error('Error removing member:', error);
      toast({
        title: 'Error',
        description: 'Failed to remove member',
        variant: 'destructive',
      });
    } finally {
      setIsRemovingMember(false);
      setShowRemoveModal(false);
      setSelectedUser(null);
    }
  };

  const handleLeaveWorkspace = async () => {
    if (!organization) return;

    setIsLeavingWorkspace(true);
    try {
      await leaveWorkspaceAction(organization.id);
    } catch (error) {
      console.error('Error leaving workspace:', error);
      toast({
        title: 'Error',
        description: 'Failed to leave workspace',
        variant: 'destructive',
      });
    } finally {
      setIsLeavingWorkspace(false);
      setShowRemoveModal(false);
      setSelectedUser(null);
    }
  };

  const handleResendInvite = (row: TableData) => {
    console.log(`Resending invite to ${row.user.email}`);
    // Add your invite resend logic here
  };

  const handleAddMembers = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true); // Set loading state

    if (!organization || !selectedRole || !emails.length) {
      toast({
        title: 'Error',
        description: 'Please fill in all required fields',
        variant: 'destructive',
      });
      setIsSubmitting(false);
      return;
    }

    try {
      const formData = new FormData();
      formData.append('emails', emails.join(','));
      formData.append('roleId', selectedRole);
      formData.append('organizationId', organization.id);

      const result = await inviteWorkspaceMembersAction(formData);

      if ('error' in result) {
        toast({
          title: 'Error',
          description: result.error,
          variant: 'destructive',
        });
        return;
      }

      if ('success' in result && result.results) {
        // Process individual results
        const successCount = result.results.filter(
          (r) => r.status === 'invited',
        ).length;
        const alreadyInvitedCount = result.results.filter(
          (r) => r.status === 'already_invited',
        ).length;
        const failedCount = result.results.filter(
          (r) => r.status === 'error' || r.status === 'email_failed',
        ).length;

        // Add new pending members to the table
        await loadMembers();

        // Update members state with new pending invitations

        // Show success toast
        let description = `Successfully invited ${successCount} member${successCount !== 1 ? 's' : ''}.`;
        if (alreadyInvitedCount > 0) {
          description += ` ${alreadyInvitedCount} already invited.`;
        }
        if (failedCount > 0) {
          description += ` ${failedCount} failed.`;
        }

        toast({
          title: 'Success',
          description,
          variant: successCount > 0 ? 'default' : 'destructive',
        });

        // Reset form
        setEmails([]);
        setSelectedRole('');
        setShowAddMemberModal(false);
      }
    } catch (error) {
      console.error('Error inviting members:', error);
      toast({
        title: 'Error',
        description: 'Failed to invite members. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false); // Reset loading state
    }
  };

  const handleActionClick = () => {
    setShowAddMemberModal(true);
  };

  const handleRoleUpdate = async (row: TableData, newRoleId: string) => {
    if (!organization || !newRoleId) return;

    try {
      const formData = new FormData();
      formData.append('userId', row.user.id); // Make sure your TableData includes user.id
      formData.append('organizationId', organization.id);
      formData.append('roleId', newRoleId);

      const result = await updateMemberRoleAction(formData);

      if ('error' in result) {
        toast({
          title: 'Error',
          description: result.error,
          variant: 'destructive',
        });
        return;
      }

      // Refresh the members list to show updated role
      await loadMembers();

      toast({
        title: 'Success',
        description: 'Member role updated successfully',
      });
    } catch (error) {
      console.error('Error updating role:', error);
      toast({
        title: 'Error',
        description: 'Failed to update member role',
        variant: 'destructive',
      });
    }
  };

  const getRoleName = (roleId: string) => {
    const role = roleOptions.find((role) => role.value === roleId);
    return role?.label.toLocaleLowerCase() || 'Unknown Role';
  };

  const columns = [
    {
      header: 'User',
      render: (row: TableData) => (
        <div className='flex items-center gap-4'>
          <Avatar className='h-10 w-10'>
            <AvatarImage src={row.user.avatarUrl} alt={row.user.name} />
            <AvatarFallback className='bg-gradient-to-b from-[#AA423A] to-[#441A17]'>
              <div className='w-full h-full rounded-full bg-black flex items-center justify-center'>
                {row.user.name.charAt(0).toUpperCase()}
              </div>
            </AvatarFallback>
          </Avatar>

          <div className='flex flex-col'>
            <span className='text-sm font-medium text-white'>
              {row.user.name}
            </span>
            <span className='text-xs text-gray-400'>{row.user.email}</span>
          </div>
        </div>
      ),
    },
    {
      header: 'Status',
      render: (row: TableData) => (
        <div className='w-fit flex items-center gap-4'>
          <StatusBadge status={row.status} customText={row.statusText} />
          {row.status === 'failed' && (
            <div
              className='flex items-center gap-1 text-xs text-[#AA423A] hover:text-white cursor-pointer'
              onClick={() => handleResendInvite(row)}
            >
              <RotateCcw className='h-3 w-3' />
              <span>Resend invite</span>
            </div>
          )}
        </div>
      ),
    },
    {
      header: 'Role',
      render: (row: TableData) => {
        const isOwner = getRoleName(row.user.role) === 'owner';

        // Create custom CTA button based on user role
        const ctaButton = !row.isPendingInvitation ? (
          <Button
            variant='destructive'
            onClick={() => {
              if (isOwner) {
                handleLeaveWorkspace();
              } else {
                handleRoleChange(row);
              }
            }}
            className='w-full mt-2 border-t border-[#313133] pt-3 text-[#AA423A] hover:text-white hover:bg-[#222224]'
          >
            {isOwner ? (
              <LogOut className='h-4 w-4 mr-2' />
            ) : (
              <Trash className='h-4 w-4 mr-2' />
            )}
            {isOwner ? 'Leave Workspace' : 'Remove Member'}
          </Button>
        ) : null;

        return (
          <SelectAccordion
            options={roleOptions}
            defaultValue={row.user.role}
            variant='bare'
            placeholder='Select role'
            disabled={row.isPendingInvitation}
            onChange={(value) => handleRoleUpdate(row, value)}
            ctaButton={ctaButton}
          />
        );
      },
    },
  ];
  const myTabs: TabItem[] = [
    {
      label: 'Members',
      content: (
        <TnkrTable<TableData>
          data={members}
          columns={columns}
          showSearch
          showAction
          actionLabel='Add Members'
          onActionClick={handleActionClick}
          isLoading={isLoading}
        />
      ),
    },
    {
      label: 'Guests',
      content: (
        <div className='flex flex-col items-center justify-center py-20 px-4'>
          <Timer className='w-16 h-16 mb-4 opacity-50' />
          <h3 className='text-xl font-medium text-white/70 mb-2'>
            Guest Management Coming Soon
          </h3>
          <p className='text-sm text-white/50 text-center max-w-md'>
            Soon you&#39;ll be able to manage guest access and temporary
            permissions for external collaborators.
          </p>
        </div>
      ),
    },
  ];

  return (
    <>
      <div className='p-12 m-5'>
        <IntroHeader
          title='Workspace Members'
          description='Manage members, access permits and roles for your organisations knowledge base.'
        />
        <div className='mt-20'>
          <Tabs
            tabs={myTabs}
            initialActiveIndex={0}
            onTabChange={(i) => console.log('switched to', i)}
          />
        </div>
      </div>

      <ModalContainer
        size='lg'
        isOpen={showRemoveModal}
        onClose={() => {
          if (!isRemovingMember) {
            setShowRemoveModal(false);
            setSelectedUser(null);
          }
        }}
      >
        <ModalBody
          icon={<WarningIcon />}
          title={`Remove ${selectedUser?.user.name} from workspace?`}
          description='They will lose access to all shared pages. To add them back in the future, an admin must invite them.'
        />
        <ModalFooter
          onCancel={() => {
            if (!isRemovingMember) {
              setShowRemoveModal(false);
              setSelectedUser(null);
            }
          }}
          onConfirm={handleConfirmRemove}
          confirmLabel={isRemovingMember ? 'Removing...' : 'Remove'}
          showCancel={true}
          cancelLabel='Cancel'
        />
      </ModalContainer>

      <ModalContainer
        isOpen={showAddMemberModal}
        size='xl'
        onClose={() => setShowAddMemberModal(false)}
      >
        <div className='p-6'>
          <div className='mb-6'>
            <h2 className='text-xl font-semibold text-white'>Invite Members</h2>
            <p className='text-sm text-white/70 mt-2'>
              Type or paste in emails below, separated by comma
            </p>
          </div>

          <form onSubmit={handleAddMembers} className='space-y-4'>
            <div className='space-y-2'>
              <Label htmlFor='emails'>Email addresses</Label>
              <EmailInput
                id='emails'
                value={emails}
                onChange={setEmails}
                placeholder='<EMAIL>, <EMAIL>'
                className='w-full mt-2'
              />
            </div>

            <div className='space-y-2'>
              <Label>Role</Label>
              <SelectAccordion
                className='mt-2'
                options={roleOptions}
                defaultValue={selectedRole}
                variant='outlined'
                placeholder='Select role'
                onChange={(value) => setSelectedRole(value)}
              />
            </div>

            <div className='flex justify-start gap-3 mt-6'>
              <Button
                className='sm:w-auto bg-[#AA423A] hover:bg-[#AA423A95] text-white'
                variant='default'
                type='submit'
                disabled={!emails.length || !selectedRole || isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <span className='mr-2'>
                      <svg
                        className='animate-spin h-4 w-4 text-white'
                        xmlns='http://www.w3.org/2000/svg'
                        fill='none'
                        viewBox='0 0 24 24'
                      >
                        <circle
                          className='opacity-25'
                          cx='12'
                          cy='12'
                          r='10'
                          stroke='currentColor'
                          strokeWidth='4'
                        ></circle>
                        <path
                          className='opacity-75'
                          fill='currentColor'
                          d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'
                        ></path>
                      </svg>
                    </span>
                    Inviting...
                  </>
                ) : (
                  'Invite Members'
                )}
              </Button>
              <Button
                type='button'
                variant='outline'
                onClick={() => setShowAddMemberModal(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            </div>
          </form>
        </div>
      </ModalContainer>
      <Toaster />
    </>
  );
}
