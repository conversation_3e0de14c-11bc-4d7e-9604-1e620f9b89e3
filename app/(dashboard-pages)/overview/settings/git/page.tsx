'use client';

import { IntroHeader } from '@/app/components/introHeader';
import { InputAccordion } from '@/app/components/InputAccordion';
import { GitHubLogoIcon } from '@radix-ui/react-icons';
import { TnkrSelect } from '@/app/components/ui/TnkrSelect';
import { Button } from '@/app/components/ui/Button';
import React, { useState, useEffect } from 'react';
import { Github, Info } from 'lucide-react';
import { Icon } from '@/app/components/CustomIcon';
import { AuthBadge } from '@/app/components/pills/AuthBadge';
import { Toaster } from '@/app/components/ui/toast';
import { useToast } from '@/app/hooks/use-toast';
import { useOrganization } from '@/app/utils/useOrganization';
import GitlabSvg from '@/assets/icons/gitlab.svg';
import BitBucketSvg from '@/assets/icons/bitbucket.svg';
interface Option {
  value: string;
  label: string;
  logo?: React.ReactNode;
}

export default function GitSettingsPage() {
  const { toast } = useToast();
  const { projects } = useOrganization();

  const [orgOptions, setOrgOptions] = useState<Option[]>([]);
  const [repoOptions, setRepoOptions] = useState<Option[]>([]);
  const [selectedOrg, setSelectedOrg] = useState<Option | undefined>(undefined);
  const [selectedRepo, setSelectedRepo] = useState<Option | undefined>(
    undefined,
  );
  const [selectedBranch, setSelectedBranch] = useState<Option | undefined>({
    value: 'main',
    label: 'main',
    logo: <Github size={16} />,
  });

  // Define the options with proper typing

  const branchOptions: Option[] = [
    { value: 'main', label: 'main', logo: <Github size={16} /> },
  ];

  const parseGitHubURL = (url: string) => {
    try {
      const [organization, repository] = url
        .replace('https://github.com/', '')
        .split('/');
      return { organization, repository };
    } catch (error) {
      console.error('Error parsing GitHub URL:', error);
      return { organization: '', repository: '' };
    }
  };

  useEffect(() => {
    if (projects && projects.length > 0) {
      // Assuming the fetched project contains `github_repo_url`
      const currentProject = projects.find((p) => p.github_repo_url);
      const githubRepoUrl = currentProject?.github_repo_url;

      if (githubRepoUrl) {
        const { organization, repository } = parseGitHubURL(githubRepoUrl);

        // Set the organization dropdown options
        setOrgOptions([
          {
            value: organization,
            label: organization,
            logo: <Github size={16} />,
          },
        ]);
        setSelectedOrg({
          value: organization,
          label: organization,
          logo: <Github size={16} />,
        });

        // Set the repository dropdown options and default value
        setRepoOptions([
          {
            value: repository,
            label: repository,
            logo: <Github size={16} />,
          },
        ]);
        setSelectedRepo({
          value: repository,
          label: repository,
          logo: <Github size={16} />,
        });
      }
    }
  }, [projects]);

  // Create handler functions to satisfy the type requirements
  const handleOrgChange = (option: Option) => setSelectedOrg(option);
  const handleRepoChange = (option: Option) => setSelectedRepo(option);
  const handleBranchChange = (option: Option) => setSelectedBranch(option);

  const handleGitlabClick = () => {
    toast({
      variant: 'modal',
      title: 'GitLab',
      description:
        'Configure GitLab to create deployments for any commits pushed to your repository. This is only available on the Enterprise plan.',
      icon: <Icon component={GitlabSvg} />,
      confirmLabel: 'Switch to GitLab',
      showCancel: false,
      onConfirm: () => {
        // Handle GitLab switch logic here
        console.log('Switching to GitLab');
      },
    });
  };

  const handleBitbucketClick = () => {
    toast({
      variant: 'modal',
      title: 'Bitbucket',
      description:
        'Configure Bitbucket to create deployments for any commits pushed to your repository. This is only available on the Enterprise plan.',
      icon: <Icon component={BitBucketSvg} />,
      confirmLabel: 'Switch to Bitbucket',
      showCancel: false,
      onConfirm: () => {
        // Handle Bitbucket switch logic here
        console.log('Switching to Bitbucket');
      },
    });
  };

  return (
    <div className='p-12 m-5'>
      <IntroHeader
        title='Git Manager'
        description='Connect your code to Tnkr and manage the source of your documentation repo.'
      />

      <div className='flex flex-col mt-10 gap-8'>
        <InputAccordion
          logo={<GitHubLogoIcon className='w-5 h-5' />}
          title='Github'
          badge={<AuthBadge status='authorized' />}
        >
          <div className='space-y-4 py-4'>
            <p className='text-white/70'>
              Configure Github to create deployments for any commits pushed to
              your repository.
            </p>
            <div className='flex items-center justify-between gap-10 mt-5'>
              <TnkrSelect
                options={orgOptions}
                label='Github organization'
                value={selectedOrg}
                onChange={handleOrgChange}
              />
              <TnkrSelect
                options={repoOptions}
                value={selectedRepo}
                label='Repository'
                onChange={handleRepoChange}
              />
              <TnkrSelect
                label='Branch'
                options={branchOptions}
                value={selectedBranch}
                onChange={handleBranchChange}
              />
            </div>
            <div className='flex items-start gap-2 p-3'>
              <Info className='w-4 h-4 text-white/70 mt-0.5 flex-shrink-0' />
              <p className='text-sm text-white/70'>
                Please make sure that a Tnkr.ai instance with a docs.json file
                exists at the selected repository.
              </p>
            </div>
            <Button variant='destructive'>Save Changes</Button>
          </div>
        </InputAccordion>
        <InputAccordion
          disabled
          logo={<Icon component={GitlabSvg} />}
          title='Gitlab'
          onClick={handleGitlabClick}
        />
        <InputAccordion
          disabled
          logo={<Icon component={BitBucketSvg} />}
          title='Bitbucket'
          onClick={handleBitbucketClick}
        />
      </div>
      <Toaster />
    </div>
  );
}
