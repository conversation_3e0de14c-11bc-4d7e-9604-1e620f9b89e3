'use client';

import React, { useState } from 'react';
import { CustomRadio } from '@/app/components/ui/CustomRadio';
import { Info, Download, Lock } from 'lucide-react';
import { useToast } from '@/app/hooks/use-toast';
import { Toaster } from '@/app/components/ui/toast';
import { IntroHeader } from '@/app/components/introHeader';

const SecurityPage = () => {
  const [openSourceEnabled, setOpenSourceEnabled] = useState(true);
  const [exportsDisabled, setExportsDisabled] = useState(true);
  const { toast } = useToast();

  const handleCopy = (text: string) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        toast({
          title: 'Copied to clipboard',
          description: 'URL copied successfully',
          duration: 2000,
        });
      })
      .catch(() => {
        toast({
          title: 'Failed to copy',
          description: 'Please try again',
          variant: 'destructive',
        });
      });
  };

  return (
    <div className='p-12 m-5'>
      <IntroHeader
        title='Security & Data'
        description='Manage who has access to your company’s knowledge base and IP'
      />

      <div className='space-y-6'>
        {/* Open Source Setting */}
        <div className='border border-border rounded-lg bg-black/20 overflow-hidden'>
          <div className='p-6'>
            <div className='flex items-start justify-between gap-4'>
              <div className='space-y-1'>
                <h2 className='text-lg font-medium'>Enable open source</h2>
                <p className='text-muted-foreground text-sm'>
                  Enable public access to all parts of your documentation, i.e
                  the version of your documentation on the `main` branch
                </p>
              </div>

              <CustomRadio
                checked={openSourceEnabled}
                onChange={setOpenSourceEnabled}
                trackClassName='bg-orange-600/30'
                knobClassName='bg-orange-500'
              />
            </div>

            {openSourceEnabled && (
              <div className='mt-4 p-4 rounded-lg bg-orange-500/10 border border-orange-500/20'>
                <div className='flex items-start gap-3'>
                  <Info className='size-5 text-orange-400 shrink-0 mt-0.5' />
                  <div className='space-y-2'>
                    <p className='text-sm text-orange-200'>
                      Your documentation is now publicly accessible
                    </p>
                    <div className='flex items-center'>
                      <code className='text-xs bg-black/30 py-1 px-2 rounded border border-orange-500/20'>
                        https://docs.yourcompany.com/public
                      </code>
                      <button
                        className='ml-2 text-xs text-orange-400 hover:text-orange-300'
                        onClick={() =>
                          handleCopy('https://docs.yourcompany.com/public')
                        }
                      >
                        Copy
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {openSourceEnabled && (
            <div className='border-t border-border p-4 bg-black/40'>
              <div className='flex items-center gap-2'>
                <span className='text-xs text-green-500'>•</span>
                <span className='text-xs text-muted-foreground'>
                  Open source enabled on April 18, 2025
                </span>
              </div>
            </div>
          )}
        </div>

        {/* Disable Exports Setting */}
        <div className='border border-border rounded-lg bg-black/20 overflow-hidden'>
          <div className='p-6'>
            <div className='flex items-start justify-between gap-4'>
              <div className='space-y-1'>
                <h2 className='text-lg font-medium'>Disable Exports</h2>
                <p className='text-muted-foreground text-sm'>
                  Prevent anyone from exporting to Markdown, CSV or PDF
                </p>
              </div>

              <CustomRadio
                checked={exportsDisabled}
                onChange={setExportsDisabled}
                trackClassName='bg-orange-600/30'
                knobClassName='bg-orange-500'
              />
            </div>

            {exportsDisabled && (
              <div className='mt-4 p-4 rounded-lg bg-orange-500/10 border border-orange-500/20'>
                <div className='flex items-start gap-3'>
                  <Lock className='size-5 text-orange-400 shrink-0 mt-0.5' />
                  <div className='space-y-2'>
                    <p className='text-sm text-orange-200'>
                      Export functionality is locked for all users
                    </p>
                    <p className='text-xs text-muted-foreground'>
                      Users will see a disabled export button with a tooltip
                      explaining that exports are disabled by the admin.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {!exportsDisabled && (
            <div className='border-t border-border p-4 bg-black/40'>
              <div className='flex flex-col md:flex-row md:items-center gap-2 md:gap-6'>
                <div className='flex items-center gap-2'>
                  <Download className='size-3 text-muted-foreground' />
                  <span className='text-xs text-muted-foreground'>
                    Downloads this month:{' '}
                    <span className='font-medium text-white'>247</span>
                  </span>
                </div>
                <div className='h-3 w-px bg-border hidden md:block'></div>
                <div className='flex items-center gap-2'>
                  <span className='size-3 rounded-full bg-green-500'></span>
                  <span className='text-xs text-muted-foreground'>
                    Most popular:{' '}
                    <span className='font-medium text-white'>PDF (64%)</span>
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      <Toaster />
    </div>
  );
};

export default SecurityPage;
