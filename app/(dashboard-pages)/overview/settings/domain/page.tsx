'use client';

import { IntroHeader } from '@/app/components/introHeader';
import { Input, InputAdornment } from '@/app/components/ui/Input';
import { Button } from '@/app/components/ui/Button';
import {
  Copy,
  PlusIcon,
  RefreshCcw,
  Trash2Icon,
  AlertTriangle,
} from 'lucide-react';
import { StatusPill } from '@/app/components/pills/StatusPill';
import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@/app/hooks/use-toast';
import TnkrTable from '@/app/components/ui/TnkrTable';
import { Toaster } from '@/app/components/ui/toast';
import {
  ModalContainer,
  ModalBody,
  ModalFooter,
} from '@/app/components/modals/Modal';
import {
  getProjectDomains,
  addAndReviewDomain,
  verifyDomain,
  deleteDomain,
  getDomainConfig,
} from '@/app/actions';
import { useTnkrOrganization } from '@/app/contexts/OrganizationContext';
interface TableData {
  type: string;
  domain: string;
  value: string;
}

interface Domain {
  name: string;
  verified: boolean;
  verification?: { type: string; value: string }[];
  records?: TableData[];
  redirect?: string | null;
  redirectStatusCode?: number | null;
  misconfigured?: boolean;
}

export default function CustomDomainPage() {
  const [domain, setDomain] = useState('');
  const [domains, setDomains] = useState<Domain[]>([]);
  const [loading, setLoading] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  // const [showRedirectModal, setShowRedirectModal] = useState(false);
  const [domainToDelete, setDomainToDelete] = useState<string | null>(null);
  // const [mainDomain, setMainDomain] = useState('');
  // const [subDomain, setSubDomain] = useState('');
  // const [isAddingRedirect, setIsAddingRedirect] = useState(false);
  const [projectId, setProjectId] = useState<string | null>(null);

  const { organization } = useTnkrOrganization();
  const { toast } = useToast();

  const DeleteModalIcon = () => (
    <div className='relative'>
      <div className='flex justify-center items-center bg-[#AA423A33] w-10 h-10 rounded-full'>
        <AlertTriangle className='text-[#AA423A]' />
      </div>
    </div>
  );

  // just a dummy function to able redirects, don't think we'd actually need this
  // const handleSetupRedirect = async () => {
  //   if (!mainDomain || !subDomain) {
  //     toast({
  //       title: 'Domains required',
  //       description: 'Please enter both main domain and subdomain',
  //       variant: 'destructive',
  //     });
  //     return;
  //   }
  //
  //   if (!isValidDomain(mainDomain) || !isValidDomain(subDomain)) {
  //     toast({
  //       title: 'Invalid domains',
  //       description: 'Please enter valid domain names',
  //       variant: 'destructive',
  //     });
  //     return;
  //   }
  //
  //   try {
  //     setIsAddingRedirect(true);
  //     await setupDomainWithRedirect(projectId, mainDomain, subDomain);
  //     await fetchDomains();
  //     setMainDomain('');
  //     setSubDomain('');
  //     setShowRedirectModal(false);
  //     toast({
  //       title: 'Redirect setup',
  //       description: `Redirect from ${subDomain} to ${mainDomain} has been configured`,
  //     });
  //   } catch (error) {
  //     toast({
  //       title: 'Failed to setup redirect',
  //       description: 'An error occurred while setting up the redirect',
  //       variant: 'destructive',
  //     });
  //   } finally {
  //     setIsAddingRedirect(false);
  //   }
  // };

  const handleCopy = (text: string) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        toast({
          title: 'Copied to clipboard',
          description: 'Value copied successfully',
          duration: 2000,
        });
      })
      .catch(() => {
        toast({
          title: 'Failed to copy',
          description: 'Please try again',
          variant: 'destructive',
        });
      });
  };

  const getStatus = (
    domain: Domain,
  ): 'idle' | 'pending' | 'success' | 'required' | 'misconfigured' => {
    if (domain.misconfigured) return 'misconfigured';
    if (domain.verified) return 'success';
    if (domain.verification?.length) return 'required';
    return 'pending';
  };

  const columns = [
    {
      header: 'Type',
      render: (row: TableData) => (
        <span className='text-gray-200'>{row.type}</span>
      ),
    },
    {
      header: 'Domain',
      render: (row: TableData) => (
        <div className='flex items-center space-x-2'>
          <span className='text-gray-200'>{row.domain}</span>
          <button
            onClick={() => handleCopy(row.domain)}
            className='text-[#DB783E] hover:text-[#DB783E]/80'
          >
            <Copy className='w-4 h-4' />
          </button>
        </div>
      ),
    },
    {
      header: 'Value',
      render: (row: TableData) => (
        <div className='flex items-center space-x-2'>
          <span className='text-gray-200 truncate'>{row.value}</span>
          <button
            onClick={() => handleCopy(row.value)}
            className='text-[#DB783E] hover:text-[#DB783E]/80'
          >
            <Copy className='w-4 h-4' />
          </button>
        </div>
      ),
    },
  ];

  const isValidDomain = (domain: string) => {
    const pattern =
      /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](\.[a-zA-Z]{2,})+$/;
    return pattern.test(domain);
  };

  const fetchDomains = useCallback(async () => {
    if (!projectId) return;

    try {
      setLoading(true);
      const domainsData = await getProjectDomains(projectId);

      // Enhanced domain data with configuration status
      const enhancedDomains = await Promise.all(
        (domainsData || []).map(async (domain) => {
          try {
            // Get configuration status for each domain
            const configResponse = await getDomainConfig(domain.name);
            return {
              ...domain,
              misconfigured: configResponse.misconfigured || false,
              configDetails: configResponse,
            };
          } catch (error) {
            console.error(`Error getting config for ${domain.name}:`, error);
            // Return the domain without config info if there was an error
            return {
              ...domain,
              misconfigured: false,
              configError: true,
            };
          }
        }),
      );

      setDomains(enhancedDomains);
    } catch (error) {
      toast({
        title: 'Error fetching domains',
        description: 'Failed to retrieve domains. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [projectId, toast, setDomains, setLoading]);

  const handleAddDomain = async () => {
    if (!projectId) {
      toast({
        title: 'Error',
        description: 'Project configuration is missing',
        variant: 'destructive',
      });
      return;
    }

    if (!domain) {
      toast({
        title: 'Domain required',
        description: 'Please enter a domain to add',
        variant: 'destructive',
      });
      return;
    }

    if (!isValidDomain(domain)) {
      toast({
        title: 'Invalid domain',
        description: 'Please enter a valid domain (e.g., example.com)',
        variant: 'destructive',
      });
      return;
    }

    try {
      setLoading(true);
      await addAndReviewDomain(projectId, domain);
      await fetchDomains();
      setDomain('');
      toast({
        title: 'Domain added',
        description: `${domain} has been added successfully`,
      });
    } catch (error) {
      toast({
        title: 'Failed to add domain',
        description: 'An error occurred while adding the domain',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleVerify = async (domainName: string) => {
    if (!projectId) return;

    try {
      setLoading(true);

      // First check domain configuration
      const configResponse = await getDomainConfig(domainName);

      // Then verify domain
      await verifyDomain(projectId, domainName);

      // Refresh domain list
      await fetchDomains();

      const configStatus = configResponse.misconfigured
        ? 'misconfigured'
        : 'properly configured';

      toast({
        title: 'Verification check',
        description: `Domain ${domainName} is ${configStatus}. Verification status updated.`,
      });
    } catch (error) {
      toast({
        title: 'Verification failed',
        description: 'Failed to verify domain status',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = (domainName: string) => {
    setDomainToDelete(domainName);
    setShowDeleteModal(true);
  };

  const handleConfirmDelete = async () => {
    if (!domainToDelete || !projectId) return;

    try {
      setLoading(true);
      await deleteDomain(projectId, domainToDelete);
      setShowDeleteModal(false);
      await fetchDomains();
      toast({
        title: 'Domain removed',
        description: `${domainToDelete} has been removed`,
      });
    } catch (error) {
      toast({
        title: 'Failed to remove domain',
        description: 'An error occurred while removing the domain',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
      setDomainToDelete(null);
    }
  };

  useEffect(() => {
    if (organization) {
      setProjectId(organization?.vercel_project_id);
      fetchDomains();
    }
  }, [projectId, fetchDomains, organization]);

  return (
    <>
      <div className='p-12 m-5'>
        <IntroHeader
          title='Custom Domain Setup'
          description='Add your custom domains to your Vercel account. These domains can be used for your projects.'
        />
        <h2>Enter your domain URL</h2>
        <div className='mt-5 max-w-xl'>
          <div className='flex flex-col gap-4'>
            <div className='flex'>
              <Input
                value={domain}
                onChange={(e) => setDomain(e.target.value)}
                placeholder='example.com or sub.example.com'
                className='pl-20'
                startAdornment={
                  <InputAdornment position='start'>https://</InputAdornment>
                }
                disabled={loading}
              />
            </div>
            <div className='flex'>
              <Button
                className='sm:w-auto bg-[#AA423A] hover:bg-[#AA423A95] text-white'
                startIcon={<PlusIcon />}
                variant='default'
                onClick={handleAddDomain}
                disabled={loading}
              >
                Add Domain
              </Button>
            </div>
            {/* // diabling redirect modla for now */}
            {/* <div className="flex-shrink-0">
              <Button
                variant="default"
                className="bg-none border border-[#313133] text-white/70 hover:border-white focus:border-white transition-colors"
                onClick={() => setShowRedirectModal(true)}
                disabled={loading}
              >
                Setup Redirect
              </Button>
            </div> */}
          </div>
        </div>

        {domains.length > 0 && (
          <div className='mt-20'>
            <IntroHeader
              hasDivider={false}
              title='Your Domains'
              description='Manage your custom domains below.'
            />
            {domains.map((d) => (
              <div key={d.name} className='mt-4'>
                <div className='flex items-center gap-4'>
                  <div className='flex-1'>
                    <Input value={d.name} disabled />
                  </div>
                  <StatusPill
                    status={getStatus(d)}
                    className='whitespace-nowrap'
                  />
                  <Button
                    variant='default'
                    className='sm:w-auto bg-none border border-[#313133] text-white/70 hover:border-white focus:border-white transition-colors'
                    startIcon={<RefreshCcw />}
                    onClick={() => handleVerify(d.name)}
                    disabled={loading || getStatus(d) === 'pending'}
                  >
                    Verify
                  </Button>
                  <Button
                    variant='default'
                    size='icon'
                    className='bg-none border border-[#313133] text-white/70 hover:border-white focus:border-white transition-colors'
                    onClick={() => handleDelete(d.name)}
                    disabled={loading || getStatus(d) === 'pending'}
                  >
                    <Trash2Icon className='h-4 w-4' />
                  </Button>
                </div>
                {d.redirect && (
                  <div className='mt-2 ml-2 text-sm text-gray-400'>
                    Redirects to: {d.redirect} ({d.redirectStatusCode || 301})
                  </div>
                )}
              </div>
            ))}
            {domains.some(
              (d) => getStatus(d) === 'required' || getStatus(d) === 'success',
            ) && (
              <div className='mt-10'>
                <IntroHeader
                  hasDivider={false}
                  title='DNS Configuration'
                  description='Add the following records to your DNS provider to verify your domains.'
                />
                {domains
                  .filter((d) => getStatus(d) === 'required')
                  .map((d) => (
                    <div key={d.name} className='mt-4'>
                      <h3 className='text-lg font-medium'>{d.name}</h3>
                      <TnkrTable<TableData>
                        columns={columns}
                        data={
                          d.records?.map((v) => ({
                            type: v.type,
                            domain: v.domain,
                            value: v.value,
                          })) || []
                        }
                      />
                    </div>
                  ))}
              </div>
            )}
          </div>
        )}
      </div>
      <ModalContainer
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
      >
        <ModalBody
          icon={<DeleteModalIcon />}
          title={`Remove ${domainToDelete}`}
          description='Existing links to this domain may break. Re-adding the domain may require reconfiguration.'
        />
        <ModalFooter
          onCancel={() => setShowDeleteModal(false)}
          onConfirm={handleConfirmDelete}
          confirmLabel='Remove'
          showCancel={true}
          cancelLabel='Cancel'
        />
      </ModalContainer>

      {/* <ModalContainer
        isOpen={showRedirectModal}
        onClose={() => setShowRedirectModal(false)}
      >
        <ModalBody
          title="Setup Domain Redirect"
          description="Create a subdomain that redirects to your main domain. The main domain must be verified first."
        >
          <div className="space-y-4 mt-4">
            <div>
              <label className="block text-sm font-medium mb-1">Main Domain</label>
              <Input
                value={mainDomain}
                onChange={(e) => setMainDomain(e.target.value)}
                placeholder="example.com"
                disabled={isAddingRedirect}
              />
              <p className="text-xs text-gray-400 mt-1">This is your primary domain that visitors will see</p>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Subdomain</label>
              <Input
                value={subDomain}
                onChange={(e) => setSubDomain(e.target.value)}
                placeholder="www.example.com"
                disabled={isAddingRedirect}
              />
              <p className="text-xs text-gray-400 mt-1">This domain will redirect to your main domain</p>
            </div>
          </div>
        </ModalBody>
        <ModalFooter
          onCancel={() => setShowRedirectModal(false)}
          onConfirm={handleSetupRedirect}
          confirmLabel="Setup Redirect"
          showCancel={true}
          cancelLabel="Cancel"
          disabled={isAddingRedirect}
        />
      </ModalContainer> */}

      <Toaster />
    </>
  );
}
