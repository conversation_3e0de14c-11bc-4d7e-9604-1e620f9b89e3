import type React from 'react';
import '@/app/globals.css';
import { ThemeProvider } from '@/app/components/ThemeProvider';
import { OrganizationProvider } from '@/app/contexts/OrganizationContext';

interface RootLayoutProps {
  children: React.ReactNode;
}

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <ThemeProvider
      attribute='class'
      defaultTheme='dark'
      forcedTheme='dark'
      enableSystem={false}
    >
      <OrganizationProvider>{children}</OrganizationProvider>
    </ThemeProvider>
  );
}
