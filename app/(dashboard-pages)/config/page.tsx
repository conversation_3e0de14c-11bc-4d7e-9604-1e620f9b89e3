'use client';

import React, { Suspense, useEffect, useState, useCallback } from 'react';
import { useSearchParams } from 'next/navigation';
import {
  GitHubLogoIcon,
  CheckIcon,
  ClipboardCopyIcon,
} from '@radix-ui/react-icons';
import { useToast } from '@/app/hooks/use-toast';
import { createClient } from '@/app/utils/supabase/client';
import { useTnkrOrganization } from '@/app/contexts/OrganizationContext';
import { Organization, Project } from '@/app/utils/useOrganization';
import Image from 'next/image';
import { Button } from '@/app/components/ui/Button';
import Link from 'next/link';

// Example assets from your message
const LOGO_URL =
  'https://res.cloudinary.com/dwpu7jpku/image/upload/v1743376108/Group_6_haqkty.png';

function ConfigForm() {
  const [currentStep, setCurrentStep] = useState<number>(1);
  const [loading, setLoading] = useState<boolean>(false);
  const [githubProject, setGithubProject] = useState<Project | null>(null);

  const { toast } = useToast();
  const searchParams = useSearchParams();
  const supabase = createClient();
  const { organization } = useTnkrOrganization();

  // -------------- Helper Functions --------------

  const saveToken = useCallback(
    async (org: Organization, token: string) => {
      setLoading(true);
      const { data: project, error } = await supabase
        .from('projects')
        .upsert(
          {
            organization_id: org.id,
            github_access_token: token,
            updated_at: new Date(),
          },
          { onConflict: 'organization_id' },
        )
        .select()
        .single();

      setLoading(false);

      if (error) {
        console.error('Error saving token:', error);
        toast({
          title: 'Error',
          description: 'Failed to save GitHub token.',
          variant: 'destructive',
        });
        return;
      }

      setGithubProject(project);
      toast({
        title: 'Success',
        description: 'GitHub token saved successfully.',
      });
      setCurrentStep(2);
    },
    [supabase, toast, setLoading, setGithubProject, setCurrentStep],
  );

  const getUserOrganizations = async (accessToken: string) => {
    const res = await fetch('https://api.github.com/user/orgs', {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        Accept: 'application/vnd.github+json',
      },
    });
    if (!res.ok) {
      throw new Error('Failed to fetch GitHub organizations');
    }
    return await res.json();
  };

  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      toast({
        title: 'Copied to clipboard',
        description: 'Command copied successfully.',
        duration: 2000,
      });
    });
  };

  const handleGithubLogin = () => {
    setLoading(true);
    const params = new URLSearchParams({
      client_id: process.env.NEXT_PUBLIC_GITHUB_CLIENT_ID ?? '',
      scope: 'repo read:org',
      redirect_uri: `${process.env.NEXT_PUBLIC_APP_URL}/api/github/callback`,
    });
    window.location.href = `https://github.com/login/oauth/authorize?${params.toString()}`;
  };

  const handleCreateRepo = async () => {
    if (!organization) {
      toast({
        title: 'Error',
        description: 'Organization not found. Please refresh the page.',
        variant: 'destructive',
      });
      return;
    }

    if (!githubProject) {
      toast({
        title: 'Error',
        description:
          'GitHub connection not found. Please reconnect your GitHub account.',
        variant: 'destructive',
      });
      return;
    }

    if (!githubProject.github_access_token) {
      toast({
        title: 'Error',
        description:
          'GitHub token not found. Please reconnect your GitHub account.',
        variant: 'destructive',
      });
      return;
    }

    setLoading(true);
    try {
      const userOrgs = await getUserOrganizations(
        githubProject.github_access_token,
      );
      const isOrg = userOrgs.length > 0;
      const selectedOrg = isOrg ? userOrgs[0].login : null;

      const repoName = `tnkr-ai-${organization.name.toLowerCase().replace(/\s+/g, '-')}-docs`;

      const response = await fetch('/api/github/create-repo', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          orgName: selectedOrg || organization.name,
          repoName,
          accessToken: githubProject.github_access_token,
          isOrg,
          orgId: organization.id,
          tnkrOrgName: organization.name,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create repository');
      }

      const result = await response.json();

      // Update githubProject with the new repo URL
      const {
        data: { html_url, id: githubRepoId, default_branch },
        tnkr_project_id,
      } = result;
      if (html_url) {
        const { data: updatedProject } = await supabase
          .from('projects')
          .update({ github_repo_url: html_url, tnkr_project_id })
          .eq('organization_id', organization.id)
          .select()
          .single();

        if (updatedProject) {
          await supabase.from('project_git_sync').insert({
            project_id: updatedProject.id,
            github_repo_id: githubRepoId?.toString(),
            branch: default_branch || 'main',
            sync_status: 'successful',
            last_synced_at: new Date(),
          });
          setGithubProject(updatedProject);
        }
      }
      toast({
        title: 'Success',
        description: 'Repository created successfully.',
      });
      setCurrentStep(3);
    } catch (error: any) {
      console.error('handleCreateRepo error:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to create repository',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // -------------- Effects --------------
  useEffect(() => {
    const token = searchParams?.get('token');
    if (token && organization) {
      saveToken(organization, token);
    } else {
      setLoading(false);
    }

    // Check for existing GitHub project
    const checkExistingProject = async () => {
      if (!organization) return;

      const { data: project } = await supabase
        .from('projects')
        .select('*')
        .eq('organization_id', organization.id)
        .single();

      if (project?.github_repo_url) {
        setGithubProject(project);
        setCurrentStep(3);
      }
    };

    checkExistingProject();
  }, [searchParams, organization, saveToken, supabase]);

  // -------------- Steps --------------
  const steps = [
    {
      id: 1,
      title: 'Connect your GitHub account',
      description: 'To get started, log in with your GitHub account',
      action: (
        <button
          disabled={loading}
          className='flex items-center rounded-xl justify-center gap-2 w-full py-2 px-4 bg-white text-black font-medium hover:bg-gray-200 transition'
          onClick={handleGithubLogin}
        >
          {loading ? (
            <div className='w-4 h-4 border-2 border-t-transparent border-black rounded-full animate-spin' />
          ) : (
            <GitHubLogoIcon />
          )}
          {loading ? 'Loading...' : 'Login with GitHub'}
        </button>
      ),
    },
    {
      id: 2,
      title: 'Create a documentation repo',
      description:
        'Your documentation content will be managed through this repo',
      action: (
        <button
          disabled={loading}
          className='flex items-center rounded-xl justify-center gap-2 w-full py-2 px-4 bg-white text-black font-medium hover:bg-gray-200 transition'
          onClick={handleCreateRepo}
        >
          {loading ? (
            <>
              <div className='w-4 h-4 border-2 border-t-transparent border-black rounded-full animate-spin' />
              <span>Creating repo...</span>
            </>
          ) : (
            'Next'
          )}
        </button>
      ),
    },
    {
      id: 3,
      title: 'Make & Deploy your first updates',
      description:
        'Clone the repo by running the following command in your terminal',
      action: (
        <div className='space-y-4'>
          <div className='flex w-full'>
            <input
              readOnly
              className='flex-1 bg-zinc-900 text-white text-sm px-4 py-2 rounded-l-md border border-white/10 font-mono'
              value={`git clone ${githubProject?.github_repo_url || ''}`}
            />
            <button
              className='bg-zinc-800 px-3 border-t border-b border-r border-white/10 rounded-r-md hover:bg-zinc-700 transition'
              onClick={() =>
                handleCopy(`git clone ${githubProject?.github_repo_url || ''}`)
              }
            >
              <ClipboardCopyIcon className='text-white' />
            </button>
          </div>
          <div className='flex w-full'>
            <input
              readOnly
              className='flex-1 bg-zinc-900 text-white text-sm px-4 py-2 rounded-l-md border border-white/10 font-mono'
              value="git add . && git commit -m 'Hello World' && git push"
            />
            <button
              className='bg-zinc-800 px-3 border-t border-b border-r border-white/10 rounded-r-md hover:bg-zinc-700 transition'
              onClick={() =>
                handleCopy(
                  "git add . && git commit -m 'Hello World' && git push",
                )
              }
            >
              <ClipboardCopyIcon className='text-white' />
            </button>
          </div>
          <button
            className='w-full bg-white text-black font-medium py-2 px-4 rounded-xl hover:bg-gray-200 transition'
            onClick={() => (window.location.href = '/overview')}
          >
            Go to dashboard
          </button>
        </div>
      ),
    },
  ];

  // -------------- Render --------------
  return (
    <div className='grid sm:grid-cols-2 grid-cols-1 h-full overflow-hidden'>
      {/* Left Column */}
      <div className='flex flex-col items-center w-full mx-auto h-screen justify-center bg-[#0D0D0D] text-white px-10 py-8'>
        {/* Logo & Heading */}
        <div>
          {/* Logo */}
          <Image
            src={LOGO_URL}
            alt='Logo'
            width={40}
            height={40}
            className='h-10 mb-8'
          />

          {/* Title */}
          <h1 className='text-2xl font-semibold mb-2'>
            Hello, {organization?.name}
          </h1>
          <p className='text-base text-gray-400'>
            Let’s get you started with documenting your robots.
          </p>
        </div>

        {/* Steps */}
        <div className='mt-12 space-y-6'>
          {steps.map((step) => {
            const isActive = step.id === currentStep;
            const isCompleted = step.id < currentStep;
            return (
              <div
                key={step.id}
                className={`relative p-4 rounded-xl border transition-all ${
                  isActive
                    ? 'border-white'
                    : isCompleted
                      ? 'border-white/20'
                      : 'border-white/10 opacity-60'
                }`}
              >
                {/* Check Icon if Completed */}
                {isCompleted && (
                  <div className='absolute top-2 right-2 bg-[#AA423A] text-black rounded-full w-4 h-4 flex items-center justify-center text-xs'>
                    <CheckIcon />
                  </div>
                )}
                <div className='text-sm font-semibold mb-1'>
                  {step.id}. {step.title}
                </div>
                <div className='text-xs text-gray-400 mb-3'>
                  {step.description}
                </div>
                {isActive && step.action && <div>{step.action}</div>}
              </div>
            );
          })}
        </div>

        {/* Footer */}
        <div className='text-sm text-gray-400 mt-10'>
          Need help?{' '}
          <a
            href='mailto:<EMAIL>'
            className='underline text-[#AA423A] hover:text-[#AA423A] transition-colors'
          >
            Contact founder
          </a>
        </div>
      </div>

      {/* Right Column (Robot Image) */}
      <div className='sm:block hidden relative w-full h-full'>
        {/* Background Image */}
        <Image
          src='https://res.cloudinary.com/dmejbgd8z/image/upload/v1732313311/thechef212_A_photograph_of_the_a_disassembled_robot_drone_with__3d21d094-bd07-4c1b-89cf-ac1b626da1b1_1_ui39zj.png'
          alt='Background'
          fill
          className='object-cover'
        />

        {/* Overlay */}
        <div className='absolute inset-0 bg-black/50' />

        {/* Content */}
        <div className='relative z-20 py-12 px-8 md:px-12 xl:px-24 flex flex-col justify-between h-full'>
          <Button
            asChild
            variant='default'
            className='relative flex items-center justify-center h-10 w-32'
            style={{
              backgroundImage: `url('https://res.cloudinary.com/dndgu2txv/image/upload/v1739017005/Tnkr_Logo_yvjruu.svg')`,
              backgroundSize: 'cover',
              backgroundColor: 'transparent',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat',
            }}
          >
            <Link href='/'></Link>
          </Button>

          <div>
            <p className='text-xl max-w-xl mb-2 text-white'>
              &quot;...It really helps to have a 3D visualisation of robots
              where you can see a breakdown of all of it&apos;s parts, It&apos;s
              a fantastic tool.&quot;
            </p>
            <p className='text-white'>
              - Ian Pritchard,
              <Link href='https://anthrobotics.ca' className='underline ml-1'>
                Anthrobotics
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function BusinessConfigPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ConfigForm />
    </Suspense>
  );
}
