// app/(auth-pages)/onboarding/page.tsx

'use client';

import { Label } from '@/app/components/ui/Label';
import { Input } from '@/app/components/ui/Input';
import { SubmitButton } from '@/app/(auth-pages)/components/SubmitButton';
import { saveBusinessDetailsAction } from '@/app/actions';
import { useRouter } from 'next/navigation';
import React, { useEffect, Suspense } from 'react';
import { useOrganization } from '@/app/utils/useOrganization';

function OnboardingForm() {
  const router = useRouter();
  const { organization, loading } = useOrganization();

  useEffect(() => {
    if (!loading && organization) {
      router.push('/overview');
    }
  }, [loading, organization, router]);

  return (
    <div className='flex flex-col min-w-64 max-w-64 mx-auto'>
      <form>
        <h1 className='text-2xl font-medium'>Get Started With Tnkr</h1>
        <div className='flex flex-col gap-2 [&>input]:mb-3 mt-8'>
          <Label htmlFor='companyName'>Company name</Label>
          <Input name='companyName' placeholder='Tnkr' required />
          <SubmitButton
            pendingText='Onboarding Company...'
            formAction={saveBusinessDetailsAction}
          >
            Continue
          </SubmitButton>
        </div>
      </form>
    </div>
  );
}

// Wrap in Suspense
export default function BusinessOnboardingPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <OnboardingForm />
    </Suspense>
  );
}
