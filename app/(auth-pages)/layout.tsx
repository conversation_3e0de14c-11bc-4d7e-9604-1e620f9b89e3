import { Button } from '@/app/components/ui/Button';
import Image from 'next/image';
import Link from 'next/link';

export default async function Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className='grid grid-cols-1 sm:grid-cols-2 h-screen w-full'>
      {/* Left section */}
      <div className='py-12 px-8 md:px-12 xl:px-24'>
        <div className='flex flex-col justify-center items-end w-auto h-full'>
          {children}
        </div>
      </div>

      {/* Right section with image */}
      <div className='sm:block hidden relative w-full h-full'>
        {/* Background Image */}
        <Image
          src='https://res.cloudinary.com/dmejbgd8z/image/upload/v1732313311/thechef212_A_photograph_of_the_a_disassembled_robot_drone_with__3d21d094-bd07-4c1b-89cf-ac1b626da1b1_1_ui39zj.png'
          alt='Background'
          fill
          className='object-cover'
        />

        {/* Overlay */}
        <div className='absolute inset-0 bg-black/50' />

        {/* Content */}
        <div className='relative z-20 py-12 px-8 md:px-12 xl:px-24 flex flex-col justify-between h-full'>
          <div>
            <Link href='/'>
              <Button
                variant='default'
                className='relative flex items-center justify-center h-10 w-32'
                style={{
                  backgroundImage: `url('https://res.cloudinary.com/dndgu2txv/image/upload/v1739017005/Tnkr_Logo_yvjruu.svg')`,
                  backgroundSize: 'cover',
                  backgroundColor: 'transparent',
                  backgroundPosition: 'center',
                  backgroundRepeat: 'no-repeat',
                }}
              />
            </Link>
          </div>

          <div>
            <p className='text-xl max-w-xl mb-2 text-white'>
              &quot;...It really helps to have a 3D visualisation of robots
              where you can see a breakdown of all of it&apos;s parts, It&apos;s
              a fantastic tool.&quot;
            </p>
            <p className='text-white'>
              - Ian Pritchard,
              <Link href='https://anthrobotics.ca' className='underline ml-1'>
                Anthrobotics
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
