'use client';

import { Button } from '@/app/components/ui/Button';
import { createClient } from '@/app/utils/supabase/client';
import { DiscordLogoIcon } from '@radix-ui/react-icons';

interface SignInWithDiscordButtonProps {
  buttonText: string;
}

export default function SignInWithDiscordButton({
  buttonText,
}: SignInWithDiscordButtonProps) {
  const supabase = createClient();

  const signInWithDiscord = async () => {
    const { error } = await supabase.auth.signInWithOAuth({
      provider: 'discord',
      options: {
        redirectTo: `${process.env.NEXT_PUBLIC_APP_URL}/auth/callback`,
        scopes: 'identify email',
      },
    });

    if (error) {
      throw error;
    }
  };

  return (
    <Button
      type='button'
      variant='outline'
      className='w-full bg-white/5 border-white/10 text-white hover:bg-white/10 py-2'
      onClick={signInWithDiscord}
    >
      <DiscordLogoIcon className='mr-2 h-4 w-4' />
      {buttonText}
    </Button>
  );
}
