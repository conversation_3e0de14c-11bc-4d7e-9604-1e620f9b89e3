'use client';

import { Button } from '@/app/components/ui/Button';
import { FcGoogle } from 'react-icons/fc'; // Assuming you have a Google icon; adjust if not
import { signInWithGoogleBusinessAction } from '@/app/actions';

interface SignInWithGoogleButtonProps {
  buttonText: string;
  inviteToken?: string;
}

export default function SignInWithGoogleButton({
  buttonText,
  inviteToken,
}: SignInWithGoogleButtonProps) {
  const handleGoogleSignIn = async () => {
    await signInWithGoogleBusinessAction(inviteToken);
  };

  return (
    <Button
      type='button'
      variant='outline'
      className='w-full bg-white/5 border-white/10 text-white hover:bg-white/10 py-2'
      onClick={handleGoogleSignIn}
    >
      <FcGoogle className='mr-2 h-4 w-4' />{' '}
      {/* Replace with your Google icon */}
      {buttonText}
    </Button>
  );
}
