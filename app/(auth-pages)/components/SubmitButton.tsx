'use client';

import { Button } from '@/app/components/ui/Button';
import { type ComponentProps } from 'react';
import { useFormStatus } from 'react-dom';

type Props = ComponentProps<typeof Button> & {
  pendingText?: string;
};

export function SubmitButton({
  children,
  pendingText = 'submitting...',
  ...props
}: Props) {
  const { pending } = useFormStatus();

  return (
    <Button
      type='submit'
      aria-disabled={pending}
      {...props}
      className='w-full sm:w-auto bg-[#AA423A] hover:bg-[#AA423A95] text-white'
    >
      {pending ? pendingText : children}
    </Button>
  );
}
