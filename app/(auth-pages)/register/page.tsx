// import { FormMessage, Message } from '@/app/(auth-pages)/components/FormMessage';
import { SubmitButton } from '@/app/(auth-pages)/components/SubmitButton';
import { signUpBusinessAction } from '@/app/actions';
import { Label } from '@/app/components/ui/Label';
import { Input } from '@/app/components/ui/Input';
import Link from 'next/link';
import SignInWithGoogleButton from '../components/SignInWithGoogleButton';

export default function BusinessSignup() {
  // if ('message' in searchParams) {
  //   return <FormMessage message={searchParams} />;
  // }

  return (
    <div className='flex flex-col min-w-64 max-w-72 mx-auto'>
      <h1 className='text-2xl font-medium'>Create Business Account</h1>
      <p className='text-sm text-foreground mt-4'>
        Already have an account?{' '}
        <Link
          className='text-[#AA423A] font-medium underline px-2'
          href='/login'
        >
          Login
        </Link>
      </p>
      <form className='flex flex-col gap-2 [&>input]:mb-3 mt-8'>
        <Label htmlFor='email'>Email</Label>
        <Input name='email' placeholder='<EMAIL>' required />
        <SubmitButton formAction={signUpBusinessAction}>Continue</SubmitButton>
        {/*<FormMessage message={searchParams} />*/}
      </form>
      <div className='relative my-4 flex items-center justify-center'>
        <div className='flex-grow h-px bg-gray-600'></div>
        <span className='px-2 text-xs uppercase text-white/400'>Or</span>
        <div className='flex-grow h-px bg-gray-600'></div>
      </div>
      <form>
        <div className='flex flex-col gap-2'>
          <SignInWithGoogleButton buttonText='Sign Up with Google' />
        </div>
      </form>
    </div>
  );
}
