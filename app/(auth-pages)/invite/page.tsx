'use client';

import React, { useEffect, useState, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { createClient } from '@/app/utils/supabase/client';
import { Label } from '@/app/components/ui/Label';
import { Button } from '@/app/components/ui/Button';
import { SubmitButton } from '@/app/(auth-pages)/components/SubmitButton';
import { Input } from '@/app/components/ui/Input';
import SignInWithGoogleButton from '@/app/(auth-pages)/components/SignInWithGoogleButton';
import { signInBusinessAction } from '@/app/actions';
import { Invitations } from '@/types/custom';
import { acceptWorkspaceInvitationAction } from '@/app/common/workspace-actions';

function InvitePageContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const token = searchParams?.get('token') || '';
  const orgName = searchParams?.get('org') || '';
  const error = searchParams?.get('error');

  const [isValidToken, setIsValidToken] = useState<boolean | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const [inviteDetails, setInviteDetails] = useState<{
    organizationName: string;
    email: string;
    expiresAt: string;
    isGeneralInvite: boolean;
  } | null>(null);
  const [loadingMessage, setLoadingMessage] = useState<string>(
    'Verifying invitation...',
  );

  useEffect(() => {
    async function checkInvitation() {
      if (!token) {
        router.push('/login?error=Invalid+invitation+link');
        return;
      }

      const supabase = createClient();

      // Check if user is authenticated
      const {
        data: { user },
      } = await supabase.auth.getUser();
      setIsAuthenticated(!!user);

      // Get invitation details
      try {
        const { data: invitation, error: inviteError } = await supabase
          .from('workspace_invitations')
          .select(
            `
            organization_id,
            email,
            status,
            expires_at,
            tnkr_organizations(
              name
            )
          `,
          )
          .eq('token', token)
          .single<Invitations>();

        if (inviteError || !invitation) {
          setIsValidToken(false);
          setLoadingMessage('Invalid or expired invitation.');
          return;
        }

        // Check if invitation is expired
        if (
          invitation.status === 'expired' ||
          new Date(invitation.expires_at) < new Date()
        ) {
          setIsValidToken(false);
          setLoadingMessage('This invitation has expired.');
          return;
        }

        setIsValidToken(true);
        console.log(invitation);
        setInviteDetails({
          organizationName: invitation.tnkr_organizations.name,
          email: invitation.email,
          expiresAt: invitation.expires_at,
          isGeneralInvite:
            invitation.email === '<EMAIL>',
        });

        // Get available roles if this is a general invite
      } catch (error) {
        console.error('Error verifying invitation:', error);
        setIsValidToken(false);
        setLoadingMessage('Failed to verify invitation.');
      }
    }

    checkInvitation();
  }, [token, router]);

  const handleAcceptInvitation = async (formData: FormData) => {
    if (!isAuthenticated) {
      // This shouldn't happen as the form shouldn't be shown
      return;
    }

    formData.append('token', token);
    await acceptWorkspaceInvitationAction(token);
    router.push('/overview');
  };
  // If still checking
  if (isValidToken === null) {
    return (
      <div className='flex flex-col items-center justify-center mx-auto p-4'>
        <div className='max-w-md w-full bg-black p-8 rounded-lg border-[#AA423A] border'>
          <h2 className='text-xl font-semibold text-white mb-4 text-center'>
            Workspace Invitation
          </h2>
          <p className='text-white/70 text-center'>{loadingMessage}</p>
          <div className='flex justify-center mt-4'>
            <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-[#AA423A]'></div>
          </div>
        </div>
      </div>
    );
  }

  // If invalid token
  if (!isValidToken) {
    return (
      <div className='flex flex-col items-center justify-center  mx-auto min-h-screen p-4'>
        <div className='max-w-md w-full bg-black p-8 rounded-lg border-red-700 border'>
          <h2 className='text-xl font-semibold text-white mb-4 text-center'>
            Invalid Invitation
          </h2>
          <p className='text-white/70 text-center'>{loadingMessage}</p>
          <div className='flex justify-center mt-6'>
            <Button
              onClick={() => router.push('/login')}
              className='bg-[#AA423A] hover:bg-[#AA423A95] text-white'
            >
              Go to Login
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // If valid token but not authenticated
  if (!isAuthenticated) {
    return (
      <div className='flex flex-col items-center justify-center min-h-screen p-4'>
        <div className='max-w-md w-full bg-black p-8 rounded-lg border-[#313133] border'>
          <h2 className='text-xl font-semibold text-white mb-2 text-center'>
            Join Workspace
          </h2>
          <p className='text-white/70 text-center mb-6'>
            You&#39;ve been invited to join{' '}
            {inviteDetails?.organizationName || orgName}. Please log in or
            create an account to continue.
          </p>

          <div className='flex flex-col gap-6'>
            <div>
              <h3 className='text-lg font-medium text-white mb-4'>Login</h3>
              <form>
                <div className='flex flex-col gap-2 [&>input]:mb-3'>
                  <Label htmlFor='email'>Email</Label>
                  <Input
                    name='email'
                    type='email'
                    placeholder='<EMAIL>'
                    required
                    defaultValue={
                      inviteDetails?.email !== '<EMAIL>'
                        ? inviteDetails?.email
                        : ''
                    }
                  />
                  <input type='hidden' name='inviteToken' value={`${token}`} />
                  <SubmitButton
                    pendingText='Signing In...'
                    formAction={signInBusinessAction}
                    className='w-full bg-[#AA423A] hover:bg-[#AA423A95] text-white'
                  >
                    Login with Email Link
                  </SubmitButton>
                </div>
              </form>

              <div className='relative my-4 flex items-center justify-center'>
                <div className='flex-grow h-px bg-gray-600'></div>
                <span className='px-2 text-xs uppercase text-white/400'>
                  Or
                </span>
                <div className='flex-grow h-px bg-gray-600'></div>
              </div>

              <div className='flex flex-col gap-4'>
                <SignInWithGoogleButton
                  inviteToken={token}
                  buttonText='Sign in with Google'
                />
              </div>
            </div>
          </div>

          {error && (
            <p className='text-sm text-red-500 mt-4'>
              {decodeURIComponent(error)}
            </p>
          )}
        </div>
      </div>
    );
  }

  // If authenticated and valid token
  return (
    <div className='flex flex-col items-center justify-center  mx-auto min-h-screen p-4'>
      <div className='bg-black p-8 rounded-lg border-[#313133] border'>
        <h2 className='text-xl font-semibold text-white mb-4'>
          Join Workspace
        </h2>
        <p className='text-white/70 mb-6'>
          You&#39;re invited to join{' '}
          <strong>{inviteDetails?.organizationName}</strong>. Click below to
          accept the invitation.
        </p>

        <form action={handleAcceptInvitation}>
          <SubmitButton
            pendingText='Joining...'
            className='w-full bg-[#AA423A] hover:bg-[#AA423A95] text-white'
          >
            Accept Invitation
          </SubmitButton>
        </form>

        {error && !error.includes('role') && (
          <p className='text-sm text-red-500 mt-4'>
            {decodeURIComponent(error)}
          </p>
        )}
      </div>
    </div>
  );
}

export default function InvitePage() {
  return (
    <Suspense
      fallback={
        <div className='flex flex-col items-center justify-center mx-auto p-4'>
          <div className='max-w-md w-full bg-black p-8 rounded-lg border-[#AA423A] border'>
            <h2 className='text-xl font-semibold text-white mb-4 text-center'>
              Loading Invitation
            </h2>
            <div className='flex justify-center mt-4'>
              <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-[#AA423A]'></div>
            </div>
          </div>
        </div>
      }
    >
      <InvitePageContent />
    </Suspense>
  );
}
