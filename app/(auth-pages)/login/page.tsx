'use client';

import { Suspense } from 'react';
import Link from 'next/link';
import { Label } from '@/app/components/ui/Label';
import { Input } from '@/app/components/ui/Input';
import { SubmitButton } from '@/app/(auth-pages)/components/SubmitButton';
import { signInBusinessAction } from '@/app/actions';
import SignInWithGoogleButton from '@/app/(auth-pages)/components/SignInWithGoogleButton';
import { useSearchParams } from 'next/navigation';

function LoginForm() {
  const searchParams = useSearchParams();
  const error = searchParams && searchParams.get('error');
  const success = searchParams && searchParams.get('success');
  const inviteToken = searchParams && searchParams.get('token');

  const handleSubmit = async (formData: FormData) => {
    if (inviteToken) {
      formData.append('inviteToken', inviteToken);
    }
    await signInBusinessAction(formData);
  };

  return (
    <div className='flex flex-col min-w-64 max-w-64 mx-auto'>
      <form action={handleSubmit}>
        <h1 className='text-2xl font-medium'>Business Login</h1>
        <p className='text-sm text-foreground mt-4'>
          Don&apos;t have an account?{' '}
          <Link
            className='text-[#AA423A] font-medium underline px-2'
            href='/register'
          >
            Get Started
          </Link>
        </p>
        <div className='flex flex-col gap-2 [&>input]:mb-3 mt-8'>
          <Label htmlFor='email'>Email</Label>
          <Input
            name='email'
            type='email'
            placeholder='<EMAIL>'
            required
          />
          <SubmitButton pendingText='Signing In...'>Login</SubmitButton>
          {error && (
            <p className='text-sm text-red-500 mt-2'>
              {decodeURIComponent(error)}
            </p>
          )}
          {success && (
            <p className='text-sm text-green-500 mt-2'>
              {decodeURIComponent(success)}
            </p>
          )}
        </div>
        <div className='relative my-4 flex items-center justify-center'>
          <div className='flex-grow h-px bg-gray-600'></div>
          <span className='px-2 text-xs uppercase text-white/400'>Or</span>
          <div className='flex-grow h-px bg-gray-600'></div>
        </div>

        <div className='flex flex-col gap-4'>
          <SignInWithGoogleButton
            buttonText='Sign in with Google'
            inviteToken={inviteToken || undefined}
          />
        </div>
      </form>
    </div>
  );
}

export default function Login() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <LoginForm />
    </Suspense>
  );
}
