{"extends": ["next/core-web-vitals", "prettier", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "ignorePatterns": ["cypress/", "cypress.config.ts", "next.config.js", "public/sw.js", "public/workbox-*.js"], "rules": {"quotes": ["error", "single", {"avoidEscape": true, "allowTemplateLiterals": true}], "jsx-quotes": ["error", "prefer-single"], "semi": ["error", "always"], "no-unused-expressions": "error", "no-unused-labels": "error", "@typescript-eslint/ban-ts-ignore": "off", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-non-null-assertion": "off", "@typescript-eslint/no-empty-function": "off", "@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/ban-types": ["error", {"extendDefaults": true, "types": {"{}": false}}]}}