import { NextResponse, type NextRequest } from 'next/server';
import { updateSession } from '@/app/utils/supabase/middleware';
import { createServerClient } from '@supabase/ssr';

const PUBLIC_ROUTES = [
  '/login',
  '/register',
  '/auth/callback',
  '/auth/confirm',
  '/invite', // Add invite route to public routes
];

// Helper function to create Supabase client in middleware
function createClient(request: NextRequest) {
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value;
        },
      },
    },
  );
}

export async function middleware(request: NextRequest) {
  const { pathname, searchParams } = request.nextUrl;

  // For invitation links
  if (pathname === '/invite') {
    const token = searchParams.get('token');

    // If no token in the invite URL, redirect to login
    if (!token) {
      return NextResponse.redirect(new URL('/login', request.url));
    }

    const response = await updateSession(request);

    // Check if user is authenticated and needs to be redirected to accept the invite
    const supabase = createClient(request);
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (user) {
      // Get organization from the invitation
      try {
        const { data: invitation } = await supabase
          .from('workspace_invitations')
          .select('organization_id')
          .eq('token', token)
          .single();

        if (invitation) {
          // Check if user is already a member of the organization
          const { data: orgUser } = await supabase
            .from('tnkr_org_users')
            .select('id')
            .eq('id', user.id)
            .eq('organization_id', invitation.organization_id)
            .is('deleted_at', null)
            .single();

          if (orgUser) {
            // User is already a member, redirect to overview
            return NextResponse.redirect(new URL('/overview', request.url));
          }
        }
      } catch (error) {
        // If error occurs, just continue with normal flow
        console.error('Error checking invitation:', error);
      }
    }

    return response;
  }

  // Allow public routes to bypass authentication checks
  if (PUBLIC_ROUTES.includes(pathname)) {
    return await updateSession(request);
  }

  // For protected routes
  if (pathname.startsWith('/overview')) {
    const supabase = createClient(request);
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.redirect(new URL('/login', request.url));
    }

    // Check if user has an organization
    const { data: orgUser } = await supabase
      .from('tnkr_org_users')
      .select('organization_id')
      .eq('id', user.id)
      .single();

    if (!orgUser) {
      // Check if user has a pending workspace invitation
      const { data: pendingInvite } = await supabase
        .from('workspace_invitations')
        .select('token')
        .eq('email', user.email)
        .eq('status', 'pending')
        .gt('expires_at', new Date().toISOString())
        .single();

      if (pendingInvite) {
        // Redirect to invite page with the token if there's a pending invitation
        return NextResponse.redirect(
          new URL(`/invite?token=${pendingInvite.token}`, request.url),
        );
      }

      return NextResponse.redirect(new URL('/onboarding', request.url));
    }

    // Check if organization has GitHub repo
    const { data: projects } = await supabase
      .from('projects')
      .select('github_repo_url')
      .eq('organization_id', orgUser.organization_id);

    const hasGitHubRepo = projects?.some(
      (proj) => proj.github_repo_url && proj.github_repo_url.trim() !== '',
    );

    if (!hasGitHubRepo) {
      return NextResponse.redirect(new URL('/config', request.url));
    }
  }

  if (pathname === '/auth/callback') {
    const inviteToken = searchParams.get('inviteToken');
    if (inviteToken) {
      const supabase = createClient(request);
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (user?.email) {
        // Update the invitation with the Google user's email
        await supabase
          .from('workspace_invitations')
          .update({ email: user.email })
          .eq('token', inviteToken)
          .eq('email', '<EMAIL>');
      }
    }
  }

  // Default behavior for other routes
  return await updateSession(request);
}

export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
};
